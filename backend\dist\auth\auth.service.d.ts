import { Repository } from 'typeorm';
import { User } from '../users/user.entity';
export declare class AuthService {
    private userRepository;
    constructor(userRepository: Repository<User>);
    verifyTelegramAuth(initData: string): Promise<boolean>;
    createOrUpdateUser(userData: {
        telegram_id: string;
        username: string;
        firstname: string;
        lastname?: string;
        language_code?: string;
    }): Promise<User>;
}
