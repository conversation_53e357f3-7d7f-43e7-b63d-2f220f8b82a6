import { <PERSON>ti<PERSON>, Column, PrimaryGeneratedColumn, ManyTo<PERSON>ne, CreateDateColumn } from 'typeorm';
import { User } from '../users/user.entity';

export enum TransactionKind {
  DEPOSIT_GIFT = 'DEPOSIT_GIFT',
  DEPOSIT_TON = 'DEPOSIT_TON',
  WITHDRAW_GIFT = 'WITHDRAW_GIFT',
  GAME_BET = 'GAME_BET',
  GAME_WIN = 'GAME_WIN',
}

@Entity()
export class Transaction {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => User)
  user: User;

  @Column()
  user_id: string;

  @Column()
  kind: TransactionKind;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount_ton: number;

  @Column({ nullable: true })
  ref: string; // reference to gift, transaction hash, etc.

  @Column({ nullable: true })
  game_type: string; // crash, coinflip, double

  @Column({ nullable: true })
  game_data: string; // JSON with game-specific data

  @CreateDateColumn()
  created_at: Date;
}
