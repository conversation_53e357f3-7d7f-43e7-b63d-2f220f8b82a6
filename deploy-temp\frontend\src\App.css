/* Premium App Layout */
.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  min-height: 100dvh;
  background: var(--bg-primary);
  position: relative;
}

/* Premium Header with Glass Effect */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: var(--space-2) var(--space-5);
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: var(--blur-xl);
  -webkit-backdrop-filter: var(--blur-xl);
  border-bottom: 0.33px solid var(--border-tertiary);

  /* Safe area support for iOS */
  padding-top: max(var(--space-2), env(safe-area-inset-top));
  padding-left: max(var(--space-5), env(safe-area-inset-left));
  padding-right: max(var(--space-5), env(safe-area-inset-right));
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 400px;
  margin: 0 auto;
  min-height: 44px;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-xl);
  font-weight: 700;
  letter-spacing: var(--tracking-tight);
  color: #FFFFFF;
  transition: all var(--transition-base) var(--ease-ios);
}

.logo:hover {
  transform: scale(1.02);
}

.balance {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  font-weight: 600;
  padding: var(--space-2) var(--space-3);
  background: var(--surface-secondary);
  border-radius: var(--radius-full);
  border: 0.33px solid var(--border-primary);
  letter-spacing: var(--tracking-normal);
  backdrop-filter: var(--blur-lg);
  -webkit-backdrop-filter: var(--blur-lg);
  transition: all var(--transition-base) var(--ease-ios);
  box-shadow: var(--shadow-sm);
}

.balance:hover {
  background: var(--surface-tertiary);
  transform: scale(1.02);
  box-shadow: var(--shadow-md);
}

/* Premium Main Content */
.app-main {
  flex: 1;
  padding: calc(var(--space-16) + var(--space-4)) var(--space-5) calc(var(--space-20) + var(--space-4));
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
  position: relative;

  /* Safe area support */
  padding-left: max(var(--space-5), env(safe-area-inset-left));
  padding-right: max(var(--space-5), env(safe-area-inset-right));
}

.page-content {
  min-height: calc(100vh - 200px);
  min-height: calc(100dvh - 200px);
}

/* Premium Bottom Navigation */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  padding: var(--space-2) var(--space-2) var(--space-5);
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: var(--blur-2xl);
  -webkit-backdrop-filter: var(--blur-2xl);
  border-top: 0.33px solid var(--border-tertiary);

  /* Safe area support */
  padding-bottom: max(var(--space-5), env(safe-area-inset-bottom));
  padding-left: max(var(--space-2), env(safe-area-inset-left));
  padding-right: max(var(--space-2), env(safe-area-inset-right));
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2) var(--space-3);
  background: transparent;
  border: none;
  color: var(--gray-1);
  font-size: var(--text-xs);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-base) var(--ease-ios);
  border-radius: var(--radius-lg);
  min-width: 44px;
  min-height: 44px;
  letter-spacing: var(--tracking-normal);
  position: relative;
  overflow: hidden;

  /* Touch optimization */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--surface-secondary);
  opacity: 0;
  transition: opacity var(--transition-fast) var(--ease-ios);
  border-radius: inherit;
}

.nav-item:hover::before {
  opacity: 0.5;
}

.nav-item svg {
  font-size: 20px;
  transition: all var(--transition-base) var(--ease-ios);
  position: relative;
  z-index: 1;
}

.nav-item span {
  position: relative;
  z-index: 1;
}

.nav-item.active {
  color: var(--ios-blue);
  background: rgba(0, 122, 255, 0.1);
  border: 0.33px solid rgba(0, 122, 255, 0.2);
}

.nav-item.active svg {
  transform: scale(1.1);
}

.nav-item:active {
  transform: scale(0.95);
  transition: transform var(--transition-fast) var(--ease-ios-in);
}

/* Premium Loading Screen */
.loading-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  min-height: 100dvh;
  gap: var(--space-6);
  text-align: center;
  background: var(--bg-primary);
  padding: var(--space-5);
}

.loading-spinner {
  margin-bottom: var(--space-4);
  animation: pulse 2s var(--ease-ios) infinite;
}

/* Premium Responsive Design */
@media (max-width: 480px) {
  .app-main {
    padding: calc(var(--space-16) + var(--space-2)) var(--space-4) calc(var(--space-20) + var(--space-2));
  }

  .header-content {
    padding: 0 var(--space-1);
    min-height: 40px;
  }

  .logo {
    font-size: var(--text-lg);
  }

  .balance {
    font-size: var(--text-xs);
    padding: var(--space-1) var(--space-2);
  }

  .nav-item {
    padding: var(--space-1) var(--space-2);
    font-size: 10px;
    min-width: 40px;
  }

  .nav-item svg {
    font-size: 18px;
  }
}

@media (max-width: 375px) {
  .app-main {
    padding: calc(var(--space-16) + var(--space-2)) var(--space-3) calc(var(--space-20) + var(--space-2));
  }

  .nav-item {
    min-width: 36px;
    padding: var(--space-1);
  }

  .nav-item svg {
    font-size: 16px;
  }
}

/* Premium Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

/* Premium Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Premium High Contrast Support */
@media (prefers-contrast: high) {
  :root {
    --border-primary: rgba(255, 255, 255, 0.8);
    --border-secondary: rgba(255, 255, 255, 0.6);
    --border-tertiary: rgba(255, 255, 255, 0.4);
  }
}
