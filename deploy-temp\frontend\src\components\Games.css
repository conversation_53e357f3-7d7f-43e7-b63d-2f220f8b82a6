/* Games Component Styles */
.games {
  min-height: 100%;
}

/* iOS-style Games Header */
.games-header {
  text-align: center;
  margin-bottom: 24px;
}

.games-header h1 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #ffffff;
  letter-spacing: -0.41px;
}

.games-header p {
  color: var(--system-gray);
  font-size: 17px;
  font-weight: 400;
  letter-spacing: -0.41px;
}

/* Games Grid */
.games-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.game-card {
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

/* Game Card Header */
.game-card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.game-icon {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
}

.game-info h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #ffffff;
  letter-spacing: -0.41px;
}

.game-description {
  color: var(--system-gray);
  font-size: 15px;
  font-weight: 500;
  margin: 0;
  letter-spacing: -0.24px;
}

/* Game Card Body */
.game-card-body {
  margin-bottom: 16px;
}

.game-subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 13px;
  line-height: 1.4;
  margin: 0;
}

/* Game Card Footer */
.game-card-footer {
  display: flex;
  justify-content: flex-end;
}

.play-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--primary-blue);
  color: #ffffff;
  border-radius: 8px;
  font-weight: 600;
  font-size: 15px;
  letter-spacing: -0.24px;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.game-card:hover .play-button {
  background: #0056CC;
  transform: translateX(2px);
}

/* iOS-style Balance Info */
.balance-info {
  padding: 20px;
  text-align: center;
}

.balance-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.balance-label {
  color: var(--system-gray);
  font-size: 17px;
  font-weight: 400;
  letter-spacing: -0.41px;
}

.balance-amount {
  font-size: 20px;
  font-weight: 600;
  letter-spacing: -0.41px;
}

.balance-note {
  color: var(--system-gray);
  font-size: 13px;
  font-weight: 400;
  letter-spacing: -0.08px;
  margin: 0;
}

/* Game Container */
.game-container {
  min-height: 100%;
}

/* Game Header */
.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 4px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(-2px);
}

.current-balance {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: var(--glass-bg);
  border: 1px solid rgba(0, 255, 136, 0.3);
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

/* Responsive */
@media (max-width: 480px) {
  .games-header h1 {
    font-size: 20px;
    gap: 8px;
  }
  
  .game-card {
    padding: 16px;
  }
  
  .game-card-header {
    gap: 12px;
    margin-bottom: 12px;
  }
  
  .game-icon {
    font-size: 28px;
    width: 40px;
    height: 40px;
  }
  
  .game-info h3 {
    font-size: 18px;
  }
  
  .game-description {
    font-size: 13px;
  }
  
  .game-subtitle {
    font-size: 12px;
  }
  
  .play-button {
    padding: 6px 12px;
    font-size: 13px;
  }
  
  .balance-info {
    padding: 16px;
  }
  
  .balance-amount {
    font-size: 16px;
  }
  
  .game-header {
    margin-bottom: 20px;
  }
  
  .back-button,
  .current-balance {
    padding: 6px 12px;
    font-size: 13px;
  }
}
