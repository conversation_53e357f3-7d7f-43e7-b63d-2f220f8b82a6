import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUser, 
  faCoins, 
  faTrophy,
  faGamepad,
  faChartLine,
  faWallet,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';
import { useTonConnectUI } from '@tonconnect/ui-react';
import ApiService from '../services/ApiService';
import './Profile.css';

const Profile = ({ user, balance }) => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [tonConnectUI] = useTonConnectUI();

  useEffect(() => {
    if (user && user.telegram_id) {
      loadUserStats();
    } else {
      setLoading(false);
    }
  }, [user]);

  const loadUserStats = async () => {
    try {
      console.log('Загрузка статистики для пользователя:', user.telegram_id);
      const userStats = await ApiService.getUserGameStats(user.telegram_id);
      console.log('Статистика загружена:', userStats);
      setStats(userStats || { total_bets: 0, total_wins: 0, total_amount: 0 });
    } catch (error) {
      console.error('Error loading user stats:', error);
      setStats({ total_bets: 0, total_wins: 0, total_amount: 0 });
    } finally {
      setLoading(false);
    }
  };

  const connectWallet = async () => {
    try {
      await tonConnectUI.connectWallet();
    } catch (error) {
      console.error('Failed to connect wallet:', error);
    }
  };

  const disconnectWallet = async () => {
    try {
      await tonConnectUI.disconnect();
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
    }
  };

  if (loading) {
    return (
      <div className="profile-loading">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <FontAwesomeIcon icon={faSpinner} size="2x" className="text-gold" />
        </motion.div>
        <p>Загрузка профиля...</p>
      </div>
    );
  }

  return (
    <div className="profile">
      {/* Header */}
      <motion.div
        className="profile-header"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1>
          <FontAwesomeIcon icon={faUser} className="text-gold" />
          Профиль
        </h1>
      </motion.div>

      {/* User Info */}
      <motion.div
        className="user-info glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <div className="user-avatar">
          <FontAwesomeIcon icon={faUser} size="2x" />
        </div>
        <div className="user-details">
          <h2>{user?.firstname || 'Пользователь'}</h2>
          <p className="username">@{user?.username || 'username'}</p>
          <p className="user-id">ID: {user?.telegram_id}</p>
        </div>
        <div className="user-balance">
          <FontAwesomeIcon icon={faCoins} className="text-green" />
          <span className="balance-amount">{balance.toFixed(2)} TON</span>
        </div>
      </motion.div>

      {/* Game Statistics */}
      <motion.div
        className="game-stats glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <h3>
          <FontAwesomeIcon icon={faChartLine} className="text-green" />
          Игровая статистика
        </h3>
        
        {stats ? (
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">
                <FontAwesomeIcon icon={faGamepad} className="text-gold" />
              </div>
              <div className="stat-info">
                <span className="stat-value">{stats.total_bets}</span>
                <span className="stat-label">Всего игр</span>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="stat-icon">
                <FontAwesomeIcon icon={faTrophy} className="text-green" />
              </div>
              <div className="stat-info">
                <span className="stat-value">{stats.total_wins}</span>
                <span className="stat-label">Побед</span>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="stat-icon">
                <FontAwesomeIcon icon={faCoins} className="text-danger" />
              </div>
              <div className="stat-info">
                <span className="stat-value">{stats.total_bet_amount.toFixed(2)}</span>
                <span className="stat-label">Поставлено TON</span>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="stat-icon">
                <FontAwesomeIcon icon={faCoins} className="text-green" />
              </div>
              <div className="stat-info">
                <span className="stat-value">{stats.total_win_amount.toFixed(2)}</span>
                <span className="stat-label">Выиграно TON</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="no-stats">
            <FontAwesomeIcon icon={faGamepad} size="2x" className="text-gold" />
            <p>Начните играть, чтобы увидеть статистику</p>
          </div>
        )}

        {stats && stats.total_bets > 0 && (
          <div className="win-rate">
            <div className="win-rate-bar">
              <div 
                className="win-rate-fill"
                style={{ 
                  width: `${(stats.total_wins / stats.total_bets * 100)}%` 
                }}
              />
            </div>
            <span className="win-rate-text">
              Процент побед: {((stats.total_wins / stats.total_bets) * 100).toFixed(1)}%
            </span>
          </div>
        )}
      </motion.div>

      {/* Game Breakdown */}
      {stats && Object.keys(stats.games).length > 0 && (
        <motion.div
          className="game-breakdown glass"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <h3>По играм</h3>
          <div className="games-list">
            {Object.entries(stats.games).map(([gameName, gameStats]) => (
              <div key={gameName} className="game-stat-item">
                <div className="game-name">
                  {gameName === 'crash' ? 'Crash' : 
                   gameName === 'coinflip' ? 'Coinflip' : 
                   gameName === 'double' ? 'Double' : gameName}
                </div>
                <div className="game-numbers">
                  <span className="games-played">{gameStats.bets} игр</span>
                  <span className="games-won text-green">{gameStats.wins} побед</span>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Wallet Connection */}
      <motion.div
        className="wallet-section glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <h3>
          <FontAwesomeIcon icon={faWallet} className="text-green" />
          TON Кошелёк
        </h3>
        
        <div className="wallet-info">
          <p>Подключите TON кошелёк для быстрых пополнений</p>
          
          <button 
            className="btn btn-primary wallet-button"
            onClick={connectWallet}
          >
            <FontAwesomeIcon icon={faWallet} />
            Подключить кошелёк
          </button>
        </div>
      </motion.div>

      {/* Account Info */}
      <motion.div
        className="account-info glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        <h3>Информация об аккаунте</h3>
        <div className="info-list">
          <div className="info-item">
            <span className="info-label">Дата регистрации:</span>
            <span className="info-value">
              {user?.created_at ? new Date(user.created_at).toLocaleDateString('ru-RU') : 'Неизвестно'}
            </span>
          </div>
          <div className="info-item">
            <span className="info-label">Статус аккаунта:</span>
            <span className="info-value text-green">Активен</span>
          </div>
          <div className="info-item">
            <span className="info-label">Версия приложения:</span>
            <span className="info-value">1.0.0</span>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Profile;
