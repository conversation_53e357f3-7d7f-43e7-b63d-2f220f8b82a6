{"version": 3, "names": ["_initializerDefineProperty", "target", "property", "descriptor", "context", "Object", "defineProperty", "enumerable", "configurable", "writable", "value", "initializer", "call"], "sources": ["../../src/helpers/initializerDefineProperty.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\ninterface DescriptorWithInitializer extends PropertyDescriptor {\n  initializer?: () => any;\n}\n\nexport default function _initializerDefineProperty<T>(\n  target: T,\n  property: PropertyKey,\n  descriptor: DescriptorWithInitializer | undefined,\n  context: DecoratorContext,\n): void {\n  if (!descriptor) return;\n\n  Object.defineProperty(target, property, {\n    enumerable: descriptor.enumerable,\n    configurable: descriptor.configurable,\n    writable: descriptor.writable,\n    value: descriptor.initializer\n      ? descriptor.initializer.call(context)\n      : void 0,\n  });\n}\n"], "mappings": ";;;;;;AAMe,SAASA,0BAA0BA,CAChDC,MAAS,EACTC,QAAqB,EACrBC,UAAiD,EACjDC,OAAyB,EACnB;EACN,IAAI,CAACD,UAAU,EAAE;EAEjBE,MAAM,CAACC,cAAc,CAACL,MAAM,EAAEC,QAAQ,EAAE;IACtCK,UAAU,EAAEJ,UAAU,CAACI,UAAU;IACjCC,YAAY,EAAEL,UAAU,CAACK,YAAY;IACrCC,QAAQ,EAAEN,UAAU,CAACM,QAAQ;IAC7BC,KAAK,EAAEP,UAAU,CAACQ,WAAW,GACzBR,UAAU,CAACQ,WAAW,CAACC,IAAI,CAACR,OAAO,CAAC,GACpC,KAAK;EACX,CAAC,CAAC;AACJ", "ignoreList": []}