# 🎰 PEPE CAS - Telegram Mini App

Современное казино-приложение для Telegram с автоматической авторизацией и интеграцией TON.

## ✨ Особенности

- 🔐 **Автоматическая авторизация** через Telegram Web Apps
- 🎨 **Современный UI** с фиолетовой цветовой схемой
- 📱 **Адаптивный дизайн** для мобильных устройств
- 🏆 **Лидерборд** и статистика игроков
- 💰 **Интеграция с TON** для платежей
- 🎮 **Игры**: Crash, Coinflip, Double
- 📊 **История транзакций** и профиль пользователя

## 🚀 Быстрый деплой

### Вариант 1: Railway (рекомендуется)
1. Форкните репозиторий
2. Зайдите на [Railway](https://railway.app)
3. Подключите GitHub репозиторий
4. Добавьте переменную `TELEGRAM_BOT_TOKEN`
5. Деплой произойдет автоматически

### Вариант 2: Render
1. Зайдите на [Render](https://render.com)
2. Создайте новый Web Service
3. Подключите репозиторий
4. Настройте команды сборки и запуска

### Вариант 3: VPS с Docker
```bash
git clone https://github.com/your-username/pepe-cas.git
cd pepe-cas
chmod +x deploy.sh
./deploy.sh
```

## 📱 Настройка Telegram бота

1. Создайте бота у @BotFather
2. Получите токен бота
3. Настройте Web App:
   - `/mybots` → выберите бота
   - "Bot Settings" → "Menu Button"
   - URL: `https://your-domain.com`
   - Текст: "🎰 Играть"

## 🛠 Локальная разработка

```bash
# Установка зависимостей
npm run install:all

# Запуск в режиме разработки
npm run dev:backend  # Terminal 1
npm run dev:frontend # Terminal 2
```

## 📦 Технологии

### Frontend
- React 18 + Vite
- Framer Motion (анимации)
- Lucide React (иконки)
- TON Connect UI

### Backend
- NestJS + TypeScript
- TypeORM + SQLite
- Telegram Bot API
- Docker

## 🔧 Переменные окружения

```env
TELEGRAM_BOT_TOKEN=your_bot_token_here
DATABASE_URL=file:./database.sqlite
PORT=3000
NODE_ENV=production
```

## 📄 Лицензия

MIT License - используйте свободно для своих проектов!
