"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TelegramService = void 0;
const common_1 = require("@nestjs/common");
let TelegramService = class TelegramService {
    botToken;
    baseUrl;
    constructor() {
        this.botToken = process.env.TELEGRAM_BOT_TOKEN || '';
        this.baseUrl = `https://api.telegram.org/bot${this.botToken}`;
    }
    async getUserProfilePhoto(userId) {
        try {
            if (!this.botToken) {
                console.warn('TELEGRAM_BOT_TOKEN not set, cannot get user photo');
                return null;
            }
            const response = await fetch(`${this.baseUrl}/getUserProfilePhotos?user_id=${userId}&limit=1`);
            if (!response.ok) {
                console.error('Failed to get user profile photos:', response.statusText);
                return null;
            }
            const data = await response.json();
            if (!data.ok || !data.result.photos || data.result.photos.length === 0) {
                console.log('No profile photos found for user:', userId);
                return null;
            }
            const photo = data.result.photos[0];
            const largestPhoto = photo[photo.length - 1];
            const fileResponse = await fetch(`${this.baseUrl}/getFile?file_id=${largestPhoto.file_id}`);
            if (!fileResponse.ok) {
                console.error('Failed to get file info:', fileResponse.statusText);
                return null;
            }
            const fileData = await fileResponse.json();
            if (!fileData.ok || !fileData.result.file_path) {
                console.error('Invalid file data received');
                return null;
            }
            const photoUrl = `https://api.telegram.org/file/bot${this.botToken}/${fileData.result.file_path}`;
            return photoUrl;
        }
        catch (error) {
            console.error('Error getting user profile photo:', error);
            return null;
        }
    }
};
exports.TelegramService = TelegramService;
exports.TelegramService = TelegramService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], TelegramService);
//# sourceMappingURL=telegram.service.js.map