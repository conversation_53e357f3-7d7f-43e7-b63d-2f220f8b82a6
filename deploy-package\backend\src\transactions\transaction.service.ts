import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Transaction, TransactionKind } from './transaction.entity';

@Injectable()
export class TransactionService {
  constructor(
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
  ) {}

  async createTransaction(
    user_id: string,
    kind: TransactionKind,
    amount_ton: number,
    ref?: string,
    game_type?: string,
    game_data?: any,
  ): Promise<Transaction> {
    const transaction = this.transactionRepository.create({
      user_id,
      kind,
      amount_ton,
      ref: ref || undefined,
      game_type: game_type || undefined,
      game_data: game_data ? JSON.stringify(game_data) : undefined,
    });
    return this.transactionRepository.save(transaction);
  }

  async getUserTransactions(user_id: string, limit = 50): Promise<Transaction[]> {
    return this.transactionRepository.find({
      where: { user_id },
      order: { created_at: 'DESC' },
      take: limit,
    });
  }

  async getAllTransactions(limit = 100): Promise<Transaction[]> {
    return this.transactionRepository.find({
      order: { created_at: 'DESC' },
      take: limit,
      relations: ['user'],
    });
  }

  async getTransactionsByKind(kind: TransactionKind, limit = 50): Promise<Transaction[]> {
    return this.transactionRepository.find({
      where: { kind },
      order: { created_at: 'DESC' },
      take: limit,
      relations: ['user'],
    });
  }

  async getTransactionsByGame(game_type: string, limit = 50): Promise<Transaction[]> {
    return this.transactionRepository.find({
      where: { game_type },
      order: { created_at: 'DESC' },
      take: limit,
      relations: ['user'],
    });
  }

  async getUserGameStats(user_id: string): Promise<any> {
    const transactions = await this.transactionRepository.find({
      where: { user_id },
      order: { created_at: 'DESC' },
    });

    const stats = {
      total_bets: 0,
      total_wins: 0,
      total_bet_amount: 0,
      total_win_amount: 0,
      games: {},
    };

    for (const tx of transactions) {
      if (tx.kind === TransactionKind.GAME_BET) {
        stats.total_bets++;
        stats.total_bet_amount += Number(tx.amount_ton);
        
        if (!stats.games[tx.game_type]) {
          stats.games[tx.game_type] = { bets: 0, wins: 0, bet_amount: 0, win_amount: 0 };
        }
        stats.games[tx.game_type].bets++;
        stats.games[tx.game_type].bet_amount += Number(tx.amount_ton);
      } else if (tx.kind === TransactionKind.GAME_WIN) {
        stats.total_wins++;
        stats.total_win_amount += Number(tx.amount_ton);
        
        if (!stats.games[tx.game_type]) {
          stats.games[tx.game_type] = { bets: 0, wins: 0, bet_amount: 0, win_amount: 0 };
        }
        stats.games[tx.game_type].wins++;
        stats.games[tx.game_type].win_amount += Number(tx.amount_ton);
      }
    }

    return stats;
  }
}
