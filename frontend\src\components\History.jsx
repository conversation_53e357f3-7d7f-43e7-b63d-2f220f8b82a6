import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faHistory, 
  faFilter, 
  faCoins,
  faGamepad,
  faGift,
  faWallet,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';
import ApiService from '../services/ApiService';
import './History.css';

const History = ({ user, balance }) => {
  const [transactions, setTransactions] = useState([]);
  const [filteredTransactions, setFilteredTransactions] = useState([]);
  const [activeFilter, setActiveFilter] = useState('all');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user && user.telegram_id) {
      loadTransactions();
    } else {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    filterTransactions();
  }, [transactions, activeFilter]);

  const loadTransactions = async () => {
    try {
      console.log('Загрузка транзакций для пользователя:', user.telegram_id);
      const data = await ApiService.getUserTransactions(user.telegram_id, 100);
      console.log('Транзакции загружены:', data);
      setTransactions(data || []);
    } catch (error) {
      console.error('Error loading transactions:', error);
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  };

  const filterTransactions = () => {
    if (activeFilter === 'all') {
      setFilteredTransactions(transactions);
    } else {
      const filtered = transactions.filter(tx => {
        switch (activeFilter) {
          case 'deposits':
            return tx.kind.includes('DEPOSIT');
          case 'games':
            return tx.kind.includes('GAME');
          case 'withdraws':
            return tx.kind.includes('WITHDRAW');
          default:
            return true;
        }
      });
      setFilteredTransactions(filtered);
    }
  };

  const filters = [
    { id: 'all', label: 'Все', icon: faHistory },
    { id: 'deposits', label: 'Пополнения', icon: faWallet },
    { id: 'games', label: 'Игры', icon: faGamepad },
    { id: 'withdraws', label: 'Выводы', icon: faGift },
  ];

  const getTransactionIcon = (kind) => {
    if (kind.includes('DEPOSIT')) return faWallet;
    if (kind.includes('GAME')) return faGamepad;
    if (kind.includes('WITHDRAW')) return faGift;
    return faCoins;
  };

  const getTransactionColor = (kind, amount) => {
    if (kind.includes('DEPOSIT') || kind === 'GAME_WIN') return 'var(--primary-green)';
    if (kind.includes('WITHDRAW') || kind === 'GAME_BET') return 'var(--danger-red)';
    return '#fff';
  };

  const formatTransactionType = (kind) => {
    switch (kind) {
      case 'DEPOSIT_GIFT': return 'Пополнение подарком';
      case 'DEPOSIT_TON': return 'Пополнение TON';
      case 'WITHDRAW_GIFT': return 'Вывод подарка';
      case 'GAME_BET': return 'Ставка в игре';
      case 'GAME_WIN': return 'Выигрыш в игре';
      default: return kind;
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      year: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="history-loading">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <FontAwesomeIcon icon={faSpinner} size="2x" className="text-gold" />
        </motion.div>
        <p>Загрузка истории...</p>
      </div>
    );
  }

  return (
    <div className="history">
      {/* Header */}
      <motion.div
        className="history-header"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1>
          <FontAwesomeIcon icon={faHistory} className="text-gold" />
          История операций
        </h1>
        <p>Все ваши транзакции и игры</p>
      </motion.div>

      {/* Filters */}
      <motion.div
        className="history-filters glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <div className="filter-header">
          <FontAwesomeIcon icon={faFilter} />
          <span>Фильтры</span>
        </div>
        <div className="filter-buttons">
          {filters.map((filter) => (
            <button
              key={filter.id}
              className={`filter-btn ${activeFilter === filter.id ? 'active' : ''}`}
              onClick={() => setActiveFilter(filter.id)}
            >
              <FontAwesomeIcon icon={filter.icon} />
              <span>{filter.label}</span>
            </button>
          ))}
        </div>
      </motion.div>

      {/* Transactions List */}
      <motion.div
        className="transactions-list"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        {filteredTransactions.length === 0 ? (
          <div className="no-transactions glass">
            <FontAwesomeIcon icon={faHistory} size="3x" className="text-gold" />
            <h3>Нет операций</h3>
            <p>
              {activeFilter === 'all' 
                ? 'У вас пока нет операций' 
                : `Нет операций в категории "${filters.find(f => f.id === activeFilter)?.label}"`
              }
            </p>
          </div>
        ) : (
          <div className="transactions-container">
            {filteredTransactions.map((transaction, index) => (
              <motion.div
                key={transaction.id}
                className="transaction-item glass"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <div className="transaction-icon">
                  <FontAwesomeIcon 
                    icon={getTransactionIcon(transaction.kind)} 
                    style={{ color: getTransactionColor(transaction.kind, transaction.amount_ton) }}
                  />
                </div>
                
                <div className="transaction-details">
                  <div className="transaction-type">
                    {formatTransactionType(transaction.kind)}
                  </div>
                  <div className="transaction-date">
                    {formatDate(transaction.created_at)}
                  </div>
                  {transaction.game_type && (
                    <div className="transaction-game">
                      Игра: {transaction.game_type}
                    </div>
                  )}
                </div>
                
                <div className="transaction-amount">
                  <span 
                    className={`amount ${
                      transaction.kind.includes('DEPOSIT') || transaction.kind === 'GAME_WIN' 
                        ? 'positive' : 'negative'
                    }`}
                  >
                    {transaction.kind.includes('DEPOSIT') || transaction.kind === 'GAME_WIN' ? '+' : '-'}
                    {parseFloat(transaction.amount_ton).toFixed(2)} TON
                  </span>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>

      {/* Summary */}
      {filteredTransactions.length > 0 && (
        <motion.div
          className="history-summary glass"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <h3>Сводка</h3>
          <div className="summary-stats">
            <div className="stat-item">
              <span className="stat-label">Всего операций:</span>
              <span className="stat-value">{filteredTransactions.length}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Общий оборот:</span>
              <span className="stat-value text-green">
                {filteredTransactions
                  .reduce((sum, tx) => sum + parseFloat(tx.amount_ton), 0)
                  .toFixed(2)} TON
              </span>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default History;
