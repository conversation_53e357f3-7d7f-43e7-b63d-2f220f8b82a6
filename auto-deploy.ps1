Write-Host "🚀 Auto-deploying PEPE CAS to Yandex Cloud" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

$SERVER_IP = "*************"
$SERVER_USER = "sexuz"
$BOT_TOKEN = "7918026591:AAHrRX3Z1tLtO7aE_gPblArhJMGTv5kixvI"

# Create deployment package
Write-Host "📦 Creating deployment package..." -ForegroundColor Blue
if (Test-Path "deploy-package") { Remove-Item -Recurse -Force "deploy-package" }
New-Item -ItemType Directory -Path "deploy-package" | Out-Null

# Copy files
Write-Host "📁 Copying files..." -ForegroundColor Yellow
Copy-Item -Recurse "backend" "deploy-package\" -Exclude @("node_modules", "dist")
Copy-Item -Recurse "frontend" "deploy-package\" -Exclude @("node_modules", "dist", "build", ".vite")
Copy-Item "package.json" "deploy-package\" -ErrorAction SilentlyContinue

# Create setup script for server
$setupScript = @'
#!/bin/bash
echo "🔧 Setting up PEPE CAS on server..."

# Update system
sudo apt update

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    echo "📦 Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Install PM2
if ! command -v pm2 &> /dev/null; then
    echo "📦 Installing PM2..."
    sudo npm install -g pm2
fi

# Install Nginx
if ! command -v nginx &> /dev/null; then
    echo "📦 Installing Nginx..."
    sudo apt install nginx -y
fi

# Build backend
echo "🔨 Building backend..."
cd backend
npm install
npm run build

# Build frontend
echo "🔨 Building frontend..."
cd ../frontend
npm install
npm run build

# Copy frontend to backend public
echo "📁 Setting up static files..."
mkdir -p ../backend/public
cp -r dist/* ../backend/public/

# Start application
echo "🚀 Starting application..."
cd ../backend
pm2 stop pepe-cas 2>/dev/null || true
pm2 start dist/main.js --name pepe-cas
pm2 save

# Setup PM2 startup
pm2 startup | tail -1 | sudo bash 2>/dev/null || true

# Configure Nginx
echo "🌐 Configuring Nginx..."
sudo tee /etc/nginx/sites-available/pepe-cas > /dev/null << 'NGINX_EOF'
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
NGINX_EOF

# Enable site
sudo ln -sf /etc/nginx/sites-available/pepe-cas /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t && sudo systemctl reload nginx

echo "✅ Deployment completed!"
echo "🌐 App is running at: http://*************"
'@

$setupScript | Out-File -FilePath "deploy-package\setup.sh" -Encoding UTF8

Write-Host "📤 Files prepared for upload" -ForegroundColor Green
Write-Host ""
Write-Host "🔧 Manual steps needed:" -ForegroundColor Yellow
Write-Host "1. Upload files: scp -r deploy-package/* sexuz@*************:~/" -ForegroundColor White
Write-Host "2. SSH to server: ssh sexuz@*************" -ForegroundColor White
Write-Host "3. Run setup: chmod +x setup.sh && ./setup.sh" -ForegroundColor White
Write-Host ""
Write-Host "🤖 Configuring Telegram bot..." -ForegroundColor Blue

# Configure Telegram bot Web App
$botApiUrl = "https://api.telegram.org/bot$BOT_TOKEN/setChatMenuButton"
$webAppUrl = "http://$SERVER_IP"

$body = @{
    menu_button = @{
        type = "web_app"
        text = "🎰 Играть"
        web_app = @{
            url = $webAppUrl
        }
    }
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-RestMethod -Uri $botApiUrl -Method POST -Body $body -ContentType "application/json"
    if ($response.ok) {
        Write-Host "✅ Telegram bot Web App configured!" -ForegroundColor Green
        Write-Host "📱 Bot menu button set to: $webAppUrl" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Failed to configure bot: $($response.description)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error configuring bot: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 Next: Upload files and run setup on server!" -ForegroundColor Green
