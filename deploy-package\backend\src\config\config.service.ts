import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GlobalConfig } from './global-config.entity';

@Injectable()
export class ConfigService {
  constructor(
    @InjectRepository(GlobalConfig)
    private configRepository: Repository<GlobalConfig>,
  ) {}

  async getConfig(key: string): Promise<string | null> {
    const config = await this.configRepository.findOne({ where: { key } });
    return config ? config.value : null;
  }

  async setConfig(key: string, value: string): Promise<GlobalConfig> {
    let config = await this.configRepository.findOne({ where: { key } });
    if (config) {
      config.value = value;
    } else {
      config = this.configRepository.create({ key, value });
    }
    return this.configRepository.save(config);
  }

  async getWinModifier(): Promise<number> {
    const modifier = await this.getConfig('win_modifier');
    return modifier ? parseFloat(modifier) : 0;
  }

  async setWinModifier(modifier: number): Promise<void> {
    // Clamp between -0.3 and 0.3 (-30% to +30%)
    const clampedModifier = Math.max(-0.3, Math.min(0.3, modifier));
    await this.setConfig('win_modifier', clampedModifier.toString());
  }

  async getAllConfigs(): Promise<GlobalConfig[]> {
    return this.configRepository.find();
  }

  async initializeDefaults(): Promise<void> {
    const defaults = [
      { key: 'win_modifier', value: '0' },
      { key: 'last_price_import', value: new Date().toISOString() },
    ];

    for (const defaultConfig of defaults) {
      const existing = await this.getConfig(defaultConfig.key);
      if (!existing) {
        await this.setConfig(defaultConfig.key, defaultConfig.value);
      }
    }
  }
}
