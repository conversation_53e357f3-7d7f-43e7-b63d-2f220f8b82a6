import { Injectable } from '@nestjs/common';
import { UserService } from '../users/user.service';
import { TransactionService } from '../transactions/transaction.service';
import { TransactionKind } from '../transactions/transaction.entity';
import { ConfigService } from '../config/config.service';
import * as crypto from 'crypto';

@Injectable()
export class GameService {
  constructor(
    private userService: UserService,
    private transactionService: TransactionService,
    private configService: ConfigService,
  ) {}

  async getWinModifier(): Promise<number> {
    const modifier = await this.configService.getConfig('win_modifier');
    return modifier ? parseFloat(modifier) : 0;
  }

  generateSeed(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  generateRandomNumber(seed: string, min: number = 0, max: number = 1): number {
    const hash = crypto.createHash('sha256').update(seed).digest('hex');
    const num = parseInt(hash.substring(0, 8), 16) / 0xffffffff;
    return min + (max - min) * num;
  }

  async placeBet(user_id: string, amount: number, game_type: string, game_data?: any): Promise<boolean> {
    const user = await this.userService.getUser(user_id);
    if (!user || user.is_banned) {
      return false;
    }

    if (Number(user.balance_ton) < amount) {
      return false;
    }

    const success = await this.userService.deductBalance(user_id, amount);
    if (success) {
      await this.transactionService.createTransaction(
        user_id,
        TransactionKind.GAME_BET,
        amount,
        undefined,
        game_type,
        game_data,
      );
    }

    return success;
  }

  async payoutWin(user_id: string, amount: number, game_type: string, game_data?: any): Promise<void> {
    await this.userService.updateBalance(user_id, amount);
    await this.transactionService.createTransaction(
      user_id,
      TransactionKind.GAME_WIN,
      amount,
      undefined,
      game_type,
      game_data,
    );
  }

  // Crash game logic
  async playCrash(user_id: string, bet_amount: number, target_multiplier: number): Promise<{
    success: boolean;
    crash_point?: number;
    payout?: number;
    seed?: string;
  }> {
    const betPlaced = await this.placeBet(user_id, bet_amount, 'crash', { target_multiplier });
    if (!betPlaced) {
      return { success: false };
    }

    const seed = this.generateSeed();
    const winModifier = await this.getWinModifier();
    
    // Generate crash point (1.0 to 10.0, with house edge and win modifier)
    const baseRandom = this.generateRandomNumber(seed);
    const adjustedRandom = Math.max(0.01, Math.min(0.99, baseRandom + winModifier));
    const crash_point = 1 / (1 - adjustedRandom) * 0.99; // 1% house edge

    if (target_multiplier <= crash_point) {
      const payout = bet_amount * target_multiplier;
      await this.payoutWin(user_id, payout, 'crash', { 
        target_multiplier, 
        crash_point, 
        payout,
        seed 
      });
      return { success: true, crash_point, payout, seed };
    }

    return { success: true, crash_point, payout: 0, seed };
  }

  // Double game logic
  async playDouble(user_id: string, bet_amount: number, chosen_multiplier: number): Promise<{
    success: boolean;
    result_multiplier?: number;
    payout?: number;
    seed?: string;
  }> {
    const validMultipliers = [2, 3, 5, 20];
    if (!validMultipliers.includes(chosen_multiplier)) {
      return { success: false };
    }

    const betPlaced = await this.placeBet(user_id, bet_amount, 'double', { chosen_multiplier });
    if (!betPlaced) {
      return { success: false };
    }

    const seed = this.generateSeed();
    const winModifier = await this.getWinModifier();
    
    // Probabilities: 2x (45%), 3x (30%), 5x (20%), 20x (5%)
    const random = this.generateRandomNumber(seed) + winModifier * 0.1;
    let result_multiplier: number;

    if (random < 0.45) {
      result_multiplier = 2;
    } else if (random < 0.75) {
      result_multiplier = 3;
    } else if (random < 0.95) {
      result_multiplier = 5;
    } else {
      result_multiplier = 20;
    }

    if (chosen_multiplier === result_multiplier) {
      const payout = bet_amount * chosen_multiplier;
      await this.payoutWin(user_id, payout, 'double', {
        chosen_multiplier,
        result_multiplier,
        payout,
        seed,
      });
      return { success: true, result_multiplier, payout, seed };
    }

    return { success: true, result_multiplier, payout: 0, seed };
  }
}
