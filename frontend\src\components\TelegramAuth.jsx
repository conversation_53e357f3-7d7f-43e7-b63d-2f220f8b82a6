import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Loader2, User, Smartphone } from 'lucide-react';
import './TelegramAuth.css';

const TelegramAuth = ({ onAuthSuccess, onAuthError }) => {
  const [authStatus, setAuthStatus] = useState('initializing');
  const [userInfo, setUserInfo] = useState(null);

  useEffect(() => {
    initializeTelegramAuth();
  }, []);

  const initializeTelegramAuth = async () => {
    try {
      setAuthStatus('checking');
      
      // Проверяем, доступны ли данные Telegram
      if (window.Telegram && window.Telegram.WebApp) {
        const tg = window.Telegram.WebApp;
        tg.ready();
        
        const user = tg.initDataUnsafe?.user;
        
        if (user) {
          setAuthStatus('authenticating');
          setUserInfo({
            id: user.id.toString(),
            firstName: user.first_name || 'Пользователь',
            username: user.username || 'user',
            photoUrl: user.photo_url
          });
          
          // Автоматически авторизуем пользователя
          setTimeout(() => {
            setAuthStatus('success');
            onAuthSuccess({
              telegram_id: user.id.toString(),
              firstname: user.first_name || 'Пользователь',
              username: user.username || 'user'
            });
          }, 1000);
        } else {
          // Fallback для тестирования
          setAuthStatus('fallback');
          const testUser = {
            id: 'test_user_' + Date.now(),
            firstName: 'Test User',
            username: 'testuser'
          };
          setUserInfo(testUser);
          
          setTimeout(() => {
            setAuthStatus('success');
            onAuthSuccess({
              telegram_id: testUser.id,
              firstname: testUser.firstName,
              username: testUser.username
            });
          }, 1500);
        }
      } else {
        // Telegram WebApp недоступен
        setAuthStatus('fallback');
        const testUser = {
          id: 'test_user_' + Date.now(),
          firstName: 'Test User',
          username: 'testuser'
        };
        setUserInfo(testUser);
        
        setTimeout(() => {
          setAuthStatus('success');
          onAuthSuccess({
            telegram_id: testUser.id,
            firstname: testUser.firstName,
            username: testUser.username
          });
        }, 1500);
      }
    } catch (error) {
      console.error('Ошибка авторизации:', error);
      setAuthStatus('error');
      onAuthError(error);
    }
  };

  const getStatusMessage = () => {
    switch (authStatus) {
      case 'initializing':
        return 'Инициализация...';
      case 'checking':
        return 'Проверка Telegram данных...';
      case 'authenticating':
        return 'Авторизация...';
      case 'fallback':
        return 'Использование тестового режима...';
      case 'success':
        return 'Авторизация успешна!';
      case 'error':
        return 'Ошибка авторизации';
      default:
        return 'Загрузка...';
    }
  };

  const getStatusIcon = () => {
    switch (authStatus) {
      case 'success':
        return <User size={32} className="text-purple" />;
      case 'error':
        return <User size={32} className="text-danger" />;
      case 'fallback':
        return <Smartphone size={32} className="text-warning" />;
      default:
        return (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <Loader2 size={32} className="text-blue" />
          </motion.div>
        );
    }
  };

  return (
    <div className="telegram-auth">
      <motion.div
        className="auth-container card-elevated"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="auth-content">
          <div className="auth-icon">
            {getStatusIcon()}
          </div>
          
          <h2 className="text-title-2 font-bold text-center">
            Добро пожаловать!
          </h2>
          
          <p className="text-callout text-secondary-label text-center">
            {getStatusMessage()}
          </p>
          
          {userInfo && (
            <motion.div
              className="user-preview"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <div className="user-avatar">
                {userInfo.photoUrl ? (
                  <img src={userInfo.photoUrl} alt="Avatar" />
                ) : (
                  <User size={24} />
                )}
              </div>
              <div className="user-details">
                <span className="user-name text-headline font-semibold">
                  {userInfo.firstName}
                </span>
                <span className="user-username text-footnote text-tertiary-label">
                  @{userInfo.username}
                </span>
              </div>
            </motion.div>
          )}
          
          {authStatus === 'fallback' && (
            <motion.div
              className="fallback-notice"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              <p className="text-footnote text-tertiary-label text-center">
                Приложение работает в тестовом режиме
              </p>
            </motion.div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default TelegramAuth;
