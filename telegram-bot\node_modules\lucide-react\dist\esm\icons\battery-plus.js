/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 9v6", key: "17i7lo" }],
  ["path", { d: "M13.5 7H16a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2h-2.5", key: "jzl4pj" }],
  ["path", { d: "M22 11v2", key: "1wo06k" }],
  ["path", { d: "M6.5 17H4a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h2.5", key: "1ar5vp" }],
  ["path", { d: "M7 12h6", key: "iekk3h" }]
];
const BatteryPlus = createLucideIcon("battery-plus", __iconNode);

export { __iconNode, BatteryPlus as default };
//# sourceMappingURL=battery-plus.js.map
