/* Deposit Component Styles */
.deposit {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Header */
.deposit-header {
  text-align: center;
  margin-bottom: 10px;
}

.deposit-header h1 {
  font-size: 24px;
  margin-bottom: 8px;
  color: #fff;
}

.deposit-header p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

/* Tabs - UIKit Style */
.deposit-tabs {
  display: flex;
  padding: 4px;
  background: var(--secondary-system-background);
  border-radius: 16px;
  gap: 4px;
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: transparent;
  border: none;
  border-radius: 12px;
  color: var(--secondary-label);
  font-size: var(--callout);
  font-weight: 600;
  cursor: pointer;
  transition: all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  min-height: 44px;
}

.tab-button.active {
  background: var(--system-blue);
  color: #FFFFFF;
  box-shadow: 0 4px 15px rgba(0, 122, 255, 0.4);
}

.tab-button:hover:not(.active) {
  background: var(--tertiary-system-background);
  color: var(--label);
}

/* Tab Content */
.tab-content {
  min-height: 400px;
}

.gift-deposit,
.ton-deposit {
  padding: 24px;
}

/* Deposit Method */
.deposit-method {
  text-align: center;
  margin-bottom: 32px;
}

.method-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.deposit-method h2 {
  font-size: 20px;
  margin-bottom: 8px;
  color: #fff;
}

.method-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.5;
}

/* Instruction Steps */
.instruction-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 24px;
}

.step {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-green);
  color: #000;
  font-weight: 700;
  font-size: 14px;
  flex-shrink: 0;
}

.step-content h3 {
  font-size: 16px;
  margin-bottom: 4px;
  color: #fff;
}

.step-content p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin: 0;
}

/* Username Copy */
.username-copy {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.username-copy code {
  flex: 1;
  color: var(--primary-green);
  font-weight: 600;
  font-size: 14px;
}

.copy-button {
  background: transparent;
  border: none;
  color: var(--primary-green);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.copy-button:hover {
  background: rgba(0, 255, 136, 0.1);
}

/* Wallet Section */
.wallet-section {
  margin-bottom: 32px;
}

.wallet-section h3 {
  font-size: 16px;
  margin-bottom: 12px;
  color: #fff;
}

.address-container {
  margin-bottom: 24px;
}

.address-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.wallet-address {
  flex: 1;
  color: var(--primary-green);
  font-weight: 600;
  font-size: 12px;
  word-break: break-all;
  line-height: 1.4;
}

/* QR Section */
.qr-section {
  text-align: center;
  margin-bottom: 24px;
}

.qr-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.qr-placeholder p {
  margin-top: 12px;
  font-size: 14px;
}

/* TON Connect Section */
.ton-connect-section {
  text-align: center;
  margin-bottom: 24px;
}

.ton-connect-section h3 {
  font-size: 16px;
  margin-bottom: 8px;
  color: #fff;
}

.ton-connect-section p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin-bottom: 16px;
}

/* Info Box */
.info-box {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 12px;
  margin-bottom: 20px;
}

.info-box p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* Last Gift */
.last-gift {
  margin-top: 24px;
}

.last-gift h3 {
  font-size: 16px;
  margin-bottom: 12px;
  color: #fff;
}

.gift-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gift-item img {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
}

.gift-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.gift-name {
  font-weight: 600;
  color: #fff;
  font-size: 14px;
}

.gift-value {
  font-weight: 700;
  font-size: 14px;
}

/* Responsive */
@media (max-width: 480px) {
  .deposit-header h1 {
    font-size: 20px;
  }
  
  .gift-deposit,
  .ton-deposit {
    padding: 20px;
  }
  
  .method-icon {
    font-size: 40px;
    margin-bottom: 12px;
  }
  
  .deposit-method h2 {
    font-size: 18px;
  }
  
  .step {
    gap: 12px;
  }
  
  .step-number {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .wallet-address {
    font-size: 11px;
  }
  
  .qr-placeholder {
    height: 160px;
  }
}
