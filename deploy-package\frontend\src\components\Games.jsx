import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Rocket,
  Coins,
  Dice6,
  ArrowLeft,
  Gamepad2
} from 'lucide-react';
import CrashGame from './games/CrashGame';
import CoinflipGame from './games/CoinflipGame';
import DoubleGame from './games/DoubleGame';
import './Games.css';

const Games = ({ user, balance, updateBalance }) => {
  const [selectedGame, setSelectedGame] = useState(null);

  const games = [
    {
      id: 'crash',
      title: 'Crash',
      icon: Rocket,
      color: 'var(--primary-green)',
      description: 'Забери до краха!',
      subtitle: 'Множитель растёт, но может упасть в любой момент',
      component: CrashGame,
    },
    {
      id: 'coinflip',
      title: 'Coinflip',
      icon: Coins,
      color: 'var(--primary-orange)',
      description: 'PvP дуэли',
      subtitle: 'Создай комнату или присоединись к игре',
      component: CoinflipGame,
    },
    {
      id: 'double',
      title: 'Double',
      icon: Dice6,
      color: 'var(--primary-red)',
      description: 'Рулетка x20',
      subtitle: 'Выбери множитель и испытай удачу',
      component: DoubleGame,
    },
  ];

  const handleGameSelect = (gameId) => {
    setSelectedGame(gameId);
  };

  const handleBackToGames = () => {
    setSelectedGame(null);
  };

  const renderGameComponent = () => {
    const game = games.find(g => g.id === selectedGame);
    if (!game) return null;

    const GameComponent = game.component;
    return (
      <GameComponent
        user={user}
        balance={balance}
        updateBalance={updateBalance}
        onBack={handleBackToGames}
      />
    );
  };

  return (
    <div className="games">
      <AnimatePresence mode="wait">
        {!selectedGame ? (
          <motion.div
            key="game-selection"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {/* Header */}
            <div className="games-header">
              <h1>
                <Gamepad2 size={24} className="text-blue" />
                Игры
              </h1>
              <p className="text-secondary">Выберите игру и испытайте удачу</p>
            </div>

            {/* Games Grid */}
            <div className="games-grid">
              {games.map((game, index) => (
                <motion.div
                  key={game.id}
                  className="game-card card"
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  onClick={() => handleGameSelect(game.id)}
                >
                  <div className="game-card-header">
                    <div
                      className="game-icon"
                      style={{ color: game.color }}
                    >
                      <game.icon size={32} />
                    </div>
                    <div className="game-info">
                      <h3>{game.title}</h3>
                      <p className="game-description">{game.description}</p>
                    </div>
                  </div>
                  
                  <div className="game-card-body">
                    <p className="game-subtitle">{game.subtitle}</p>
                  </div>

                  <div className="game-card-footer">
                    <div className="play-button">
                      <span>Играть</span>
                      <game.icon size={16} />
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Balance Info */}
            <motion.div
              className="balance-info card"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <div className="balance-display">
                <span className="balance-label">Ваш баланс:</span>
                <span className="balance-amount text-green">
                  {balance.toFixed(2)} TON
                </span>
              </div>
              <p className="balance-note">
                Убедитесь, что у вас достаточно средств для игры
              </p>
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            key="game-component"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="game-container"
          >
            {/* Game Header */}
            <div className="game-header">
              <button
                className="back-button"
                onClick={handleBackToGames}
              >
                <ArrowLeft size={16} />
                <span>Назад</span>
              </button>
              <div className="current-balance">
                <Coins size={16} className="text-green" />
                <span>{balance.toFixed(2)} TON</span>
              </div>
            </div>

            {/* Game Component */}
            {renderGameComponent()}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Games;
