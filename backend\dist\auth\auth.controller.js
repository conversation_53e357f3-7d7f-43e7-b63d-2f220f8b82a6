"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const auth_service_1 = require("./auth.service");
let AuthController = class AuthController {
    authService;
    constructor(authService) {
        this.authService = authService;
    }
    async authenticateTelegram(body) {
        try {
            const { initData, user } = body;
            if (!initData || !user) {
                throw new common_1.BadRequestException('Missing initData or user');
            }
            const isValid = await this.authService.verifyTelegramAuth(initData);
            if (!isValid) {
                throw new common_1.BadRequestException('Invalid Telegram signature');
            }
            const userData = await this.authService.createOrUpdateUser({
                telegram_id: user.id.toString(),
                username: user.username || 'user',
                firstname: user.first_name || 'Пользователь',
                lastname: user.last_name || '',
                language_code: user.language_code || 'ru'
            });
            return {
                success: true,
                user: userData,
                verified: true
            };
        }
        catch (error) {
            console.error('Telegram auth error:', error);
            throw new common_1.BadRequestException('Authentication failed');
        }
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('telegram'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "authenticateTelegram", null);
exports.AuthController = AuthController = __decorate([
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map