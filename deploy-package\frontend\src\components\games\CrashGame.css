/* Crash Game Styles */
.crash-game {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Game Display */
.game-display {
  padding: 20px;
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.game-header h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  color: #fff;
  margin: 0;
}

.current-multiplier {
  padding: 8px 16px;
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid rgba(0, 255, 136, 0.3);
  border-radius: 12px;
}

.multiplier {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-green);
  transition: all 0.3s ease;
}

.multiplier.playing {
  animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Game Canvas */
.game-canvas-container {
  position: relative;
  width: 100%;
  height: 200px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
}

.game-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.canvas-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  color: rgba(255, 255, 255, 0.7);
  gap: 12px;
}

.canvas-overlay p {
  margin: 0;
  font-size: 14px;
}

/* Game Result */
.game-result {
  text-align: center;
  padding: 20px;
  border-radius: 12px;
  border: 2px solid;
}

.game-result.won {
  background: rgba(0, 255, 136, 0.1);
  border-color: var(--primary-green);
}

.game-result.lost {
  background: rgba(255, 71, 87, 0.1);
  border-color: var(--danger-red);
}

.result-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.result-text {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #fff;
}

.result-details p {
  margin: 4px 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.payout {
  font-weight: 700;
  font-size: 16px;
}

/* Game Controls */
.game-controls {
  padding: 20px;
}

.control-group {
  margin-bottom: 20px;
}

.control-group label {
  display: block;
  margin-bottom: 8px;
  color: #fff;
  font-weight: 600;
  font-size: 14px;
}

.input-group {
  position: relative;
  margin-bottom: 12px;
}

.input-group .input {
  width: 100%;
  padding-right: 40px;
}

.input-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
}

/* Quick Buttons */
.quick-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-btn {
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-btn:hover:not(:disabled) {
  background: rgba(0, 255, 136, 0.2);
  border-color: var(--primary-green);
}

.quick-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Play Button */
.play-button {
  width: 100%;
  padding: 16px;
  font-size: 16px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 8px;
}

.play-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Game History */
.game-history {
  padding: 20px;
}

.game-history h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  margin-bottom: 16px;
  color: #fff;
}

.history-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.history-item {
  padding: 8px 12px;
  border-radius: 8px;
  font-weight: 700;
  font-size: 12px;
  min-width: 50px;
  text-align: center;
  border: 1px solid;
}

.history-item.high {
  background: rgba(0, 255, 136, 0.1);
  border-color: var(--primary-green);
  color: var(--primary-green);
}

.history-item.low {
  background: rgba(255, 71, 87, 0.1);
  border-color: var(--danger-red);
  color: var(--danger-red);
}

/* Responsive */
@media (max-width: 480px) {
  .game-display,
  .game-controls,
  .game-history {
    padding: 16px;
  }
  
  .game-header h2 {
    font-size: 16px;
  }
  
  .multiplier {
    font-size: 20px;
  }
  
  .game-canvas-container {
    height: 160px;
  }
  
  .result-icon {
    font-size: 36px;
    margin-bottom: 8px;
  }
  
  .result-text {
    font-size: 18px;
    margin-bottom: 12px;
  }
  
  .result-details p {
    font-size: 13px;
  }
  
  .control-group {
    margin-bottom: 16px;
  }
  
  .quick-buttons {
    gap: 6px;
  }
  
  .quick-btn {
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .play-button {
    padding: 14px;
    font-size: 15px;
  }
}
