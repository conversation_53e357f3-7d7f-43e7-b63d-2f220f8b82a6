# 🚀 Деплой PEPE CAS Mini App

## Быстрый старт

### 1. Подготовка сервера
```bash
# Установите Docker и Docker Compose на сервер
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. Настройка Telegram бота
1. Создайте бота у @BotFather в Telegram
2. Получите токен бота
3. Отредактируйте `backend/.env`:
```env
TELEGRAM_BOT_TOKEN=your_actual_bot_token_here
DATABASE_URL=file:./database.sqlite
PORT=3000
NODE_ENV=production
```

### 3. Деплой
```bash
chmod +x deploy.sh
./deploy.sh
```

## Настройка Telegram Web App

### 1. В @BotFather:
1. Отправьте `/mybots`
2. Выберите вашего бота
3. "Bot Settings" → "Menu Button"
4. "Configure menu button"
5. URL: `https://your-domain.com`
6. Текст: "🎰 Играть"

## Рекомендуемые хостинги

### VPS (рекомендуется)
- **DigitalOcean**: $6/месяц (1GB RAM)
- **Vultr**: $3.50/месяц
- **Hetzner**: €3/месяц

### Cloud Platforms
- **Railway**: $5/месяц
- **Render**: $7/месяц

## SSL и домен
1. Купите домен
2. Настройте A-запись на IP сервера
3. Используйте Let's Encrypt для SSL

## Команды управления
```bash
# Просмотр логов
docker-compose logs -f

# Перезапуск
docker-compose restart

# Остановка
docker-compose down

# Обновление
git pull && docker-compose build --no-cache && docker-compose up -d
```
