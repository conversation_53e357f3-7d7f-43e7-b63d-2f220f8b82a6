var P;function Js(t){P={...P,...t}}function w(t){return{lang:t?.lang??P?.lang,message:t?.message,abortEarly:t?.abortEarly??P?.abortEarly,abortPipeEarly:t?.abortPipeEarly??P?.abortPipeEarly}}function Zs(){P=void 0}var q;function Ys(t,n){q||(q=new Map),q.set(n,t)}function G(t){return q?.get(t)}function eu(t){q?.delete(t)}var _;function tu(t,n){_||(_=new Map),_.set(n,t)}function U(t){return _?.get(t)}function su(t){_?.delete(t)}var M;function ru(t,n,e){M||(M=new Map),M.get(t)||M.set(t,new Map),M.get(t).set(e,n)}function X(t,n){return M?.get(t)?.get(n)}function ou(t,n){M?.get(t)?.delete(n)}function f(t){let n=typeof t;return n==="string"?`"${t}"`:n==="number"||n==="bigint"||n==="boolean"?`${t}`:n==="object"||n==="function"?(t&&Object.getPrototypeOf(t)?.constructor?.name)??"null":n}function r(t,n,e,s,u){let o=u&&"input"in u?u.input:e.value,a=u?.expected??t.expects??null,I=u?.received??f(o),i={kind:t.kind,type:t.type,input:o,expected:a,received:I,message:`Invalid ${n}: ${a?`Expected ${a} but r`:"R"}eceived ${I}`,requirement:t.requirement,path:u?.path,issues:u?.issues,lang:s.lang,abortEarly:s.abortEarly,abortPipeEarly:s.abortPipeEarly},m=t.kind==="schema",c=u?.message??t.message??X(t.reference,i.lang)??(m?U(i.lang):null)??s.message??G(i.lang);c!==void 0&&(i.message=typeof c=="function"?c(i):c),m&&(e.typed=!1),e.issues?e.issues.push(i):e.issues=[i]}var N;function A(t){return N||(N=new TextEncoder),N.encode(t).length}var C;function b(t){C||(C=new Intl.Segmenter);let n=C.segment(t),e=0;for(let s of n)e++;return e}function p(t){return{version:1,vendor:"valibot",validate(n){return t["~run"]({value:n},w())}}}var D;function E(t,n){D||(D=new Map),D.get(t)||D.set(t,new Intl.Segmenter(t,{granularity:"word"}));let e=D.get(t).segment(n),s=0;for(let u of e)u.isWordLike&&s++;return s}var je=/\D/gu;function W(t){let n=t.replace(je,""),e=n.length,s=1,u=0;for(;e;){let o=+n[--e];s^=1,u+=s?[0,2,4,6,8,1,3,5,7,9][o]:o}return u%10===0}function g(t,n){return Object.hasOwn(t,n)&&n!=="__proto__"&&n!=="prototype"&&n!=="constructor"}function k(t,n){let e=[...new Set(t)];return e.length>1?`(${e.join(` ${n} `)})`:e[0]??"never"}function xu(t,n){let e={};for(let s of t)e[s]=n;return e}function gu(t){let n={};for(let e of t)Object.assign(n,e.entries);return n}function H(t){if(t.path){let n="";for(let e of t.path)if(typeof e.key=="string"||typeof e.key=="number")n?n+=`.${e.key}`:n+=e.key;else return null;return n}return null}function Bu(t,n){return n.kind===t}function bu(t,n){return n.type===t}function Pu(t){return t instanceof O}var O=class extends Error{constructor(n){super(n[0].message),this.name="ValiError",this.issues=n}};function ve(t){return{kind:"transformation",type:"args",reference:ve,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=(...u)=>{let o=this.schema["~run"]({value:u},e);if(o.issues)throw new O(o.issues);return s(...o.value)},n}}}function qe(t){return{kind:"transformation",type:"args",reference:qe,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=async(...u)=>{let o=await t["~run"]({value:u},e);if(o.issues)throw new O(o.issues);return s(...o.value)},n}}}function _e(){return{kind:"transformation",type:"await",reference:_e,async:!0,async"~run"(t){return t.value=await t.value,t}}}var J=/^(?:[\da-z+/]{4})*(?:[\da-z+/]{2}==|[\da-z+/]{3}=)?$/iu,Z=/^[A-Z]{6}(?!00)[\dA-Z]{2}(?:[\dA-Z]{3})?$/u,Q=/^[a-z][\da-z]*$/u,Y=/^[+-]?(?:\d*\.)?\d+$/u,ee=/^\d+$/u,ne=/^[\w+-]+(?:\.[\w+-]+)*@[\da-z]+(?:[.-][\da-z]+)*\.[a-z]{2,}$/iu,te=/^(?:[\u{1F1E6}-\u{1F1FF}]{2}|\u{1F3F4}[\u{E0061}-\u{E007A}]{2}[\u{E0030}-\u{E0039}\u{E0061}-\u{E007A}]{1,3}\u{E007F}|(?:\p{Emoji}\uFE0F\u20E3?|\p{Emoji_Modifier_Base}\p{Emoji_Modifier}?|\p{Emoji_Presentation})(?:\u200D(?:\p{Emoji}\uFE0F\u20E3?|\p{Emoji_Modifier_Base}\p{Emoji_Modifier}?|\p{Emoji_Presentation}))*)+$/u,se=/^(?:0[hx])?[\da-fA-F]+$/u,ue=/^#(?:[\da-fA-F]{3,4}|[\da-fA-F]{6}|[\da-fA-F]{8})$/u,re=/^\d{15}$|^\d{2}-\d{6}-\d{6}-\d$/u,oe=/^(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])(?:\.(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])){3}$/u,ae=/^(?:(?:[\da-f]{1,4}:){7}[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,7}:|(?:[\da-f]{1,4}:){1,6}:[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,5}(?::[\da-f]{1,4}){1,2}|(?:[\da-f]{1,4}:){1,4}(?::[\da-f]{1,4}){1,3}|(?:[\da-f]{1,4}:){1,3}(?::[\da-f]{1,4}){1,4}|(?:[\da-f]{1,4}:){1,2}(?::[\da-f]{1,4}){1,5}|[\da-f]{1,4}:(?::[\da-f]{1,4}){1,6}|:(?:(?::[\da-f]{1,4}){1,7}|:)|fe80:(?::[\da-f]{0,4}){0,4}%[\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d)|(?:[\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d))$/iu,ie=/^(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])(?:\.(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])){3}$|^(?:(?:[\da-f]{1,4}:){7}[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,7}:|(?:[\da-f]{1,4}:){1,6}:[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,5}(?::[\da-f]{1,4}){1,2}|(?:[\da-f]{1,4}:){1,4}(?::[\da-f]{1,4}){1,3}|(?:[\da-f]{1,4}:){1,3}(?::[\da-f]{1,4}){1,4}|(?:[\da-f]{1,4}:){1,2}(?::[\da-f]{1,4}){1,5}|[\da-f]{1,4}:(?::[\da-f]{1,4}){1,6}|:(?:(?::[\da-f]{1,4}){1,7}|:)|fe80:(?::[\da-f]{0,4}){0,4}%[\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d)|(?:[\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d))$/iu,Ie=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])$/u,pe=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])[T ](?:0\d|1\d|2[0-3]):[0-5]\d$/u,me=/^(?:0\d|1\d|2[0-3]):[0-5]\d$/u,ce=/^(?:0\d|1\d|2[0-3])(?::[0-5]\d){2}$/u,Te=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])[T ](?:0\d|1\d|2[0-3])(?::[0-5]\d){2}(?:\.\d{1,9})?(?:Z|[+-](?:0\d|1\d|2[0-3])(?::?[0-5]\d)?)$/u,de=/^\d{4}-W(?:0[1-9]|[1-4]\d|5[0-3])$/u,fe=/^(?:[\da-f]{2}:){5}[\da-f]{2}$|^(?:[\da-f]{2}-){5}[\da-f]{2}$|^(?:[\da-f]{4}\.){2}[\da-f]{4}$/iu,ye=/^(?:[\da-f]{2}:){7}[\da-f]{2}$|^(?:[\da-f]{2}-){7}[\da-f]{2}$|^(?:[\da-f]{4}\.){3}[\da-f]{4}$|^(?:[\da-f]{4}:){3}[\da-f]{4}$/iu,le=/^(?:[\da-f]{2}:){5}[\da-f]{2}$|^(?:[\da-f]{2}-){5}[\da-f]{2}$|^(?:[\da-f]{4}\.){2}[\da-f]{4}$|^(?:[\da-f]{2}:){7}[\da-f]{2}$|^(?:[\da-f]{2}-){7}[\da-f]{2}$|^(?:[\da-f]{4}\.){3}[\da-f]{4}$|^(?:[\da-f]{4}:){3}[\da-f]{4}$/iu,he=/^[\w-]+$/u,ke=/^(?:0o)?[0-7]+$/u,xe=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,Oe=/^[\da-z]+(?:[-_][\da-z]+)*$/u,ge=/^[\da-hjkmnp-tv-zA-HJKMNP-TV-Z]{26}$/u,we=/^[\da-f]{8}(?:-[\da-f]{4}){3}-[\da-f]{12}$/iu;function De(t){return{kind:"validation",type:"base64",reference:De,async:!1,expects:null,requirement:J,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Base64",n,e),n}}}function We(t){return{kind:"validation",type:"bic",reference:We,async:!1,expects:null,requirement:Z,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"BIC",n,e),n}}}function Ve(t){return{kind:"transformation",type:"brand",reference:Ve,async:!1,name:t,"~run"(n){return n}}}function Ne(t,n){return{kind:"validation",type:"bytes",reference:Ne,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u!==this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function Ce(t,n){return{kind:"validation",type:"check",reference:Ce,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement(e.value)&&r(this,"input",e,s),e}}}function Le(t,n){return{kind:"validation",type:"check",reference:Le,async:!0,expects:null,requirement:t,message:n,async"~run"(e,s){return e.typed&&!await this.requirement(e.value)&&r(this,"input",e,s),e}}}function Ke(t,n){return{kind:"validation",type:"check_items",reference:Ke,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){if(e.typed)for(let u=0;u<e.value.length;u++){let o=e.value[u];this.requirement(o,u,e.value)||r(this,"item",e,s,{input:o,path:[{type:"array",origin:"value",input:e.value,key:u,value:o}]})}return e}}}function $e(t,n){return{kind:"validation",type:"check_items",reference:$e,async:!0,expects:null,requirement:t,message:n,async"~run"(e,s){if(e.typed){let u=await Promise.all(e.value.map(this.requirement));for(let o=0;o<e.value.length;o++)if(!u[o]){let a=e.value[o];r(this,"item",e,s,{input:a,path:[{type:"array",origin:"value",input:e.value,key:o,value:a}]})}}return e}}}var Fe=/^(?:\d{14,19}|\d{4}(?: \d{3,6}){2,4}|\d{4}(?:-\d{3,6}){2,4})$/u,ze=/[- ]/gu,Ge=[/^3[47]\d{13}$/u,/^3(?:0[0-5]|[68]\d)\d{11,13}$/u,/^6(?:011|5\d{2})\d{12,15}$/u,/^(?:2131|1800|35\d{3})\d{11}$/u,/^5[1-5]\d{2}|(?:222\d|22[3-9]\d|2[3-6]\d{2}|27[01]\d|2720)\d{12}$/u,/^(?:6[27]\d{14,17}|81\d{14,17})$/u,/^4\d{12}(?:\d{3,6})?$/u];function Ue(t){return{kind:"validation",type:"credit_card",reference:Ue,async:!1,expects:null,requirement(n){let e;return Fe.test(n)&&(e=n.replace(ze,""))&&Ge.some(s=>s.test(e))&&W(e)},message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"credit card",n,e),n}}}function Xe(t){return{kind:"validation",type:"cuid2",reference:Xe,async:!1,expects:null,requirement:Q,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Cuid2",n,e),n}}}function He(t){return{kind:"validation",type:"decimal",reference:He,async:!1,expects:null,requirement:Y,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"decimal",n,e),n}}}function Je(t){return{kind:"metadata",type:"description",reference:Je,description:t}}function Ze(t){return{kind:"validation",type:"digits",reference:Ze,async:!1,expects:null,requirement:ee,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"digits",n,e),n}}}function Qe(t){return{kind:"validation",type:"email",reference:Qe,expects:null,async:!1,requirement:ne,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"email",n,e),n}}}function Ye(t){return{kind:"validation",type:"emoji",reference:Ye,async:!1,expects:null,requirement:te,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"emoji",n,e),n}}}function en(t){return{kind:"validation",type:"empty",reference:en,async:!1,expects:"0",message:t,"~run"(n,e){return n.typed&&n.value.length>0&&r(this,"length",n,e,{received:`${n.value.length}`}),n}}}function nn(t,n){return{kind:"validation",type:"ends_with",reference:nn,async:!1,expects:`"${t}"`,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.endsWith(this.requirement)&&r(this,"end",e,s,{received:`"${e.value.slice(-this.requirement.length)}"`}),e}}}function tn(t,n){return{kind:"validation",type:"every_item",reference:tn,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.every(this.requirement)&&r(this,"item",e,s),e}}}function sn(t,n){let e=f(t);return{kind:"validation",type:"excludes",reference:sn,async:!1,expects:`!${e}`,requirement:t,message:n,"~run"(s,u){return s.typed&&s.value.includes(this.requirement)&&r(this,"content",s,u,{received:e}),s}}}function un(t){return{kind:"transformation",type:"filter_items",reference:un,async:!1,operation:t,"~run"(n){return n.value=n.value.filter(this.operation),n}}}function rn(t){return{kind:"transformation",type:"find_item",reference:rn,async:!1,operation:t,"~run"(n){return n.value=n.value.find(this.operation),n}}}function on(t){return{kind:"validation",type:"finite",reference:on,async:!1,expects:null,requirement:Number.isFinite,message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"finite",n,e),n}}}function an(t,n){return{kind:"validation",type:"graphemes",reference:an,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=b(e.value);u!==this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function In(t,n){return{kind:"validation",type:"gt_value",reference:In,async:!1,expects:`>${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value>this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}var pn={md4:32,md5:32,sha1:40,sha256:64,sha384:96,sha512:128,ripemd128:32,ripemd160:40,tiger128:32,tiger160:40,tiger192:48,crc32:8,crc32b:8,adler32:8};function mn(t,n){return{kind:"validation",type:"hash",reference:mn,expects:null,async:!1,requirement:RegExp(t.map(e=>`^[a-f0-9]{${pn[e]}}$`).join("|"),"iu"),message:n,"~run"(e,s){return e.typed&&!this.requirement.test(e.value)&&r(this,"hash",e,s),e}}}function cn(t){return{kind:"validation",type:"hexadecimal",reference:cn,async:!1,expects:null,requirement:se,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"hexadecimal",n,e),n}}}function Tn(t){return{kind:"validation",type:"hex_color",reference:Tn,async:!1,expects:null,requirement:ue,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"hex color",n,e),n}}}function dn(t){return{kind:"validation",type:"imei",reference:dn,async:!1,expects:null,requirement(n){return re.test(n)&&W(n)},message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"IMEI",n,e),n}}}function fn(t,n){let e=f(t);return{kind:"validation",type:"includes",reference:fn,async:!1,expects:e,requirement:t,message:n,"~run"(s,u){return s.typed&&!s.value.includes(this.requirement)&&r(this,"content",s,u,{received:`!${e}`}),s}}}function yn(t){return{kind:"validation",type:"integer",reference:yn,async:!1,expects:null,requirement:Number.isInteger,message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"integer",n,e),n}}}function ln(t){return{kind:"validation",type:"ip",reference:ln,async:!1,expects:null,requirement:ie,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IP",n,e),n}}}function hn(t){return{kind:"validation",type:"ipv4",reference:hn,async:!1,expects:null,requirement:oe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IPv4",n,e),n}}}function kn(t){return{kind:"validation",type:"ipv6",reference:kn,async:!1,expects:null,requirement:ae,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IPv6",n,e),n}}}function xn(t){return{kind:"validation",type:"iso_date",reference:xn,async:!1,expects:null,requirement:Ie,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"date",n,e),n}}}function On(t){return{kind:"validation",type:"iso_date_time",reference:On,async:!1,expects:null,requirement:pe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"date-time",n,e),n}}}function gn(t){return{kind:"validation",type:"iso_time",reference:gn,async:!1,expects:null,requirement:me,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"time",n,e),n}}}function wn(t){return{kind:"validation",type:"iso_time_second",reference:wn,async:!1,expects:null,requirement:ce,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"time-second",n,e),n}}}function Sn(t){return{kind:"validation",type:"iso_timestamp",reference:Sn,async:!1,expects:null,requirement:Te,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"timestamp",n,e),n}}}function Bn(t){return{kind:"validation",type:"iso_week",reference:Bn,async:!1,expects:null,requirement:de,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"week",n,e),n}}}function An(t,n){return{kind:"validation",type:"length",reference:An,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length!==this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function bn(t,n){return{kind:"validation",type:"lt_value",reference:bn,async:!1,expects:`<${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value<this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function En(t){return{kind:"validation",type:"mac",reference:En,async:!1,expects:null,requirement:le,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"MAC",n,e),n}}}function Mn(t){return{kind:"validation",type:"mac48",reference:Mn,async:!1,expects:null,requirement:fe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"48-bit MAC",n,e),n}}}function Pn(t){return{kind:"validation",type:"mac64",reference:Pn,async:!1,expects:null,requirement:ye,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"64-bit MAC",n,e),n}}}function Rn(t){return{kind:"transformation",type:"map_items",reference:Rn,async:!1,operation:t,"~run"(n){return n.value=n.value.map(this.operation),n}}}function jn(t,n){return{kind:"validation",type:"max_bytes",reference:jn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u>this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function vn(t,n){return{kind:"validation",type:"max_graphemes",reference:vn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=b(e.value);u>this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function qn(t,n){return{kind:"validation",type:"max_length",reference:qn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length>this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function _n(t,n){return{kind:"validation",type:"max_size",reference:_n,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size>this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function Dn(t,n){return{kind:"validation",type:"max_value",reference:Dn,async:!1,expects:`<=${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value<=this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Wn(t,n,e){return{kind:"validation",type:"max_words",reference:Wn,async:!1,expects:`<=${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=E(this.locales,s.value);o>this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function Vn(t){return{kind:"metadata",type:"metadata",reference:Vn,metadata:t}}function Nn(t,n){return{kind:"validation",type:"mime_type",reference:Nn,async:!1,expects:k(t.map(e=>`"${e}"`),"|"),requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement.includes(e.value.type)&&r(this,"MIME type",e,s,{received:`"${e.value.type}"`}),e}}}function Cn(t,n){return{kind:"validation",type:"min_bytes",reference:Cn,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u<this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function Ln(t,n){return{kind:"validation",type:"min_graphemes",reference:Ln,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=b(e.value);u<this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function Kn(t,n){return{kind:"validation",type:"min_length",reference:Kn,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length<this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function $n(t,n){return{kind:"validation",type:"min_size",reference:$n,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size<this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function Fn(t,n){return{kind:"validation",type:"min_value",reference:Fn,async:!1,expects:`>=${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value>=this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function zn(t,n,e){return{kind:"validation",type:"min_words",reference:zn,async:!1,expects:`>=${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=E(this.locales,s.value);o<this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function Gn(t,n){return{kind:"validation",type:"multiple_of",reference:Gn,async:!1,expects:`%${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value%this.requirement!==0&&r(this,"multiple",e,s),e}}}function Un(t){return{kind:"validation",type:"nanoid",reference:Un,async:!1,expects:null,requirement:he,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Nano ID",n,e),n}}}function Xn(t){return{kind:"validation",type:"non_empty",reference:Xn,async:!1,expects:"!0",message:t,"~run"(n,e){return n.typed&&n.value.length===0&&r(this,"length",n,e,{received:"0"}),n}}}function Hn(t){return{kind:"transformation",type:"normalize",reference:Hn,async:!1,form:t,"~run"(n){return n.value=n.value.normalize(this.form),n}}}function Jn(t,n){return{kind:"validation",type:"not_bytes",reference:Jn,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u===this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function Zn(t,n){return{kind:"validation",type:"not_graphemes",reference:Zn,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=b(e.value);u===this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function Qn(t,n){return{kind:"validation",type:"not_length",reference:Qn,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length===this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function Yn(t,n){return{kind:"validation",type:"not_size",reference:Yn,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size===this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function et(t,n){return{kind:"validation",type:"not_value",reference:et,async:!1,expects:t instanceof Date?`!${t.toJSON()}`:`!${f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&this.requirement<=e.value&&this.requirement>=e.value&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function nt(t,n){return{kind:"validation",type:"not_values",reference:nt,async:!1,expects:`!${k(t.map(e=>e instanceof Date?e.toJSON():f(e)),"|")}`,requirement:t,message:n,"~run"(e,s){return e.typed&&this.requirement.some(u=>u<=e.value&&u>=e.value)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function tt(t,n,e){return{kind:"validation",type:"not_words",reference:tt,async:!1,expects:`!${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=E(this.locales,s.value);o===this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function st(t){return{kind:"validation",type:"octal",reference:st,async:!1,expects:null,requirement:ke,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"octal",n,e),n}}}function V(t,n){if(t.issues)for(let e of n)for(let s of t.issues){let u=!1,o=Math.min(e.length,s.path?.length??0);for(let a=0;a<o;a++)if(e[a]!==s.path[a].key&&(e[a]!=="$"||s.path[a].type!=="array")){u=!0;break}if(!u)return!1}return!0}function ut(t,n,e){return{kind:"validation",type:"partial_check",reference:ut,async:!1,expects:null,paths:t,requirement:n,message:e,"~run"(s,u){return(s.typed||V(s,t))&&!this.requirement(s.value)&&r(this,"input",s,u),s}}}function rt(t,n,e){return{kind:"validation",type:"partial_check",reference:rt,async:!0,expects:null,paths:t,requirement:n,message:e,async"~run"(s,u){return(s.typed||V(s,t))&&!await this.requirement(s.value)&&r(this,"input",s,u),s}}}function ot(t){return{kind:"validation",type:"raw_check",reference:ot,async:!1,expects:null,"~run"(n,e){return t({dataset:n,config:e,addIssue:s=>r(this,s?.label??"input",n,e,s)}),n}}}function at(t){return{kind:"validation",type:"raw_check",reference:at,async:!0,expects:null,async"~run"(n,e){return await t({dataset:n,config:e,addIssue:s=>r(this,s?.label??"input",n,e,s)}),n}}}function it(t){return{kind:"transformation",type:"raw_transform",reference:it,async:!1,"~run"(n,e){let s=t({dataset:n,config:e,addIssue:u=>r(this,u?.label??"input",n,e,u),NEVER:null});return n.issues?n.typed=!1:n.value=s,n}}}function It(t){return{kind:"transformation",type:"raw_transform",reference:It,async:!0,async"~run"(n,e){let s=await t({dataset:n,config:e,addIssue:u=>r(this,u?.label??"input",n,e,u),NEVER:null});return n.issues?n.typed=!1:n.value=s,n}}}function pt(){return{kind:"transformation",type:"readonly",reference:pt,async:!1,"~run"(t){return t}}}function mt(t,n){return{kind:"transformation",type:"reduce_items",reference:mt,async:!1,operation:t,initial:n,"~run"(e){return e.value=e.value.reduce(this.operation,this.initial),e}}}function ct(t,n){return{kind:"validation",type:"regex",reference:ct,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement.test(e.value)&&r(this,"format",e,s),e}}}function Tt(t){return{kind:"transformation",type:"returns",reference:Tt,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=(...u)=>{let o=this.schema["~run"]({value:s(...u)},e);if(o.issues)throw new O(o.issues);return o.value},n}}}function dt(t){return{kind:"transformation",type:"returns",reference:dt,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=async(...u)=>{let o=await this.schema["~run"]({value:await s(...u)},e);if(o.issues)throw new O(o.issues);return o.value},n}}}function ft(t){return{kind:"validation",type:"rfc_email",reference:ft,expects:null,async:!1,requirement:xe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"email",n,e),n}}}function yt(t){return{kind:"validation",type:"safe_integer",reference:yt,async:!1,expects:null,requirement:Number.isSafeInteger,message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"safe integer",n,e),n}}}function lt(t,n){return{kind:"validation",type:"size",reference:lt,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size!==this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function ht(t){return{kind:"validation",type:"slug",reference:ht,async:!1,expects:null,requirement:Oe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"slug",n,e),n}}}function kt(t,n){return{kind:"validation",type:"some_item",reference:kt,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.some(this.requirement)&&r(this,"item",e,s),e}}}function xt(t){return{kind:"transformation",type:"sort_items",reference:xt,async:!1,operation:t,"~run"(n){return n.value=n.value.sort(this.operation),n}}}function Ot(t,n){return{kind:"validation",type:"starts_with",reference:Ot,async:!1,expects:`"${t}"`,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.startsWith(this.requirement)&&r(this,"start",e,s,{received:`"${e.value.slice(0,this.requirement.length)}"`}),e}}}function gt(t){return{kind:"metadata",type:"title",reference:gt,title:t}}function wt(){return{kind:"transformation",type:"to_lower_case",reference:wt,async:!1,"~run"(t){return t.value=t.value.toLowerCase(),t}}}function St(t){return{kind:"transformation",type:"to_max_value",reference:St,async:!1,requirement:t,"~run"(n){return n.value=n.value>this.requirement?this.requirement:n.value,n}}}function Bt(t){return{kind:"transformation",type:"to_min_value",reference:Bt,async:!1,requirement:t,"~run"(n){return n.value=n.value<this.requirement?this.requirement:n.value,n}}}function At(){return{kind:"transformation",type:"to_upper_case",reference:At,async:!1,"~run"(t){return t.value=t.value.toUpperCase(),t}}}function bt(t){return{kind:"transformation",type:"transform",reference:bt,async:!1,operation:t,"~run"(n){return n.value=this.operation(n.value),n}}}function Et(t){return{kind:"transformation",type:"transform",reference:Et,async:!0,operation:t,async"~run"(n){return n.value=await this.operation(n.value),n}}}function Mt(){return{kind:"transformation",type:"trim",reference:Mt,async:!1,"~run"(t){return t.value=t.value.trim(),t}}}function Pt(){return{kind:"transformation",type:"trim_end",reference:Pt,async:!1,"~run"(t){return t.value=t.value.trimEnd(),t}}}function Rt(){return{kind:"transformation",type:"trim_start",reference:Rt,async:!1,"~run"(t){return t.value=t.value.trimStart(),t}}}function jt(t){return{kind:"validation",type:"ulid",reference:jt,async:!1,expects:null,requirement:ge,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"ULID",n,e),n}}}function vt(t){return{kind:"validation",type:"url",reference:vt,async:!1,expects:null,requirement(n){try{return new URL(n),!0}catch{return!1}},message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"URL",n,e),n}}}function qt(t){return{kind:"validation",type:"uuid",reference:qt,async:!1,expects:null,requirement:we,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"UUID",n,e),n}}}function _t(t,n){return{kind:"validation",type:"value",reference:_t,async:!1,expects:t instanceof Date?t.toJSON():f(t),requirement:t,message:n,"~run"(e,s){return e.typed&&!(this.requirement<=e.value&&this.requirement>=e.value)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Dt(t,n){return{kind:"validation",type:"values",reference:Dt,async:!1,expects:`${k(t.map(e=>e instanceof Date?e.toJSON():f(e)),"|")}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement.some(u=>u<=e.value&&u>=e.value)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Wt(t,n,e){return{kind:"validation",type:"words",reference:Wt,async:!1,expects:`${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=E(this.locales,s.value);o!==this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function Gi(t,n){let e=t["~run"]({value:n},{abortEarly:!0}).issues;if(e)throw new O(e)}function Hi(t,n){return{...t,get"~standard"(){return p(this)},"~run"(e,s){return t["~run"](e,{...s,...n})}}}function l(t,n,e){return typeof t.fallback=="function"?t.fallback(n,e):t.fallback}function eI(t,n){return{...t,fallback:n,get"~standard"(){return p(this)},"~run"(e,s){let u=t["~run"](e,s);return u.issues?{typed:!0,value:l(this,u,s)}:u}}}function uI(t,n){return{...t,fallback:n,async:!0,get"~standard"(){return p(this)},async"~run"(e,s){let u=await t["~run"](e,s);return u.issues?{typed:!0,value:await l(this,u,s)}:u}}}function aI(t){let n={};for(let e of t)if(e.path){let s=H(e);s?(n.nested||(n.nested={}),n.nested[s]?n.nested[s].push(e.message):n.nested[s]=[e.message]):n.other?n.other.push(e.message):n.other=[e.message]}else n.root?n.root.push(e.message):n.root=[e.message];return n}function II(t,n){return{...t,"~run"(e,s){let u=e.issues&&[...e.issues];if(e=t["~run"](e,s),e.issues){for(let o of e.issues)if(!u?.includes(o)){let a=e.value;for(let I of n){let i=a[I],m={type:"unknown",origin:"value",input:a,key:I,value:i};if(o.path?o.path.push(m):o.path=[m],!i)break;a=i}}}return e}}}function mI(t,n){return{...t,async:!0,async"~run"(e,s){let u=e.issues&&[...e.issues];if(e=await t["~run"](e,s),e.issues){for(let o of e.issues)if(!u?.includes(o)){let a=e.value;for(let I of n){let i=a[I],m={type:"unknown",origin:"value",input:a,key:I,value:i};if(o.path?o.path.push(m):o.path=[m],!i)break;a=i}}}return e}}}function y(t,n,e){return typeof t.default=="function"?t.default(n,e):t.default}function Se(t){if("entries"in t){let n={};for(let e in t.entries)n[e]=Se(t.entries[e]);return n}return"items"in t?t.items.map(Se):y(t)}async function Be(t){return"entries"in t?Object.fromEntries(await Promise.all(Object.entries(t.entries).map(async([n,e])=>[n,await Be(e)]))):"items"in t?Promise.all(t.items.map(Be)):y(t)}function Ae(t){if("entries"in t){let n={};for(let e in t.entries)n[e]=Ae(t.entries[e]);return n}return"items"in t?t.items.map(Ae):l(t)}async function be(t){return"entries"in t?Object.fromEntries(await Promise.all(Object.entries(t.entries).map(async([n,e])=>[n,await be(e)]))):"items"in t?Promise.all(t.items.map(be)):l(t)}function gI(t,n){return!t["~run"]({value:n},{abortEarly:!0}).issues}function Vt(){return{kind:"schema",type:"any",reference:Vt,expects:"any",async:!1,get"~standard"(){return p(this)},"~run"(t){return t.typed=!0,t}}}function Nt(t,n){return{kind:"schema",type:"array",reference:Nt,expects:"Array",async:!1,item:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<u.length;o++){let a=u[o],I=this.item["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}}else r(this,"type",e,s);return e}}}function Ct(t,n){return{kind:"schema",type:"array",reference:Ct,expects:"Array",async:!0,item:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(u.map(a=>this.item["~run"]({value:a},s)));for(let a=0;a<o.length;a++){let I=o[a];if(I.issues){let i={type:"array",origin:"value",input:u,key:a,value:u[a]};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}}else r(this,"type",e,s);return e}}}function Lt(t){return{kind:"schema",type:"bigint",reference:Lt,expects:"bigint",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="bigint"?n.typed=!0:r(this,"type",n,e),n}}}function Kt(t){return{kind:"schema",type:"blob",reference:Kt,expects:"Blob",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof Blob?n.typed=!0:r(this,"type",n,e),n}}}function $t(t){return{kind:"schema",type:"boolean",reference:$t,expects:"boolean",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="boolean"?n.typed=!0:r(this,"type",n,e),n}}}function Ft(t,n){return{kind:"schema",type:"custom",reference:Ft,expects:"unknown",async:!1,check:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return this.check(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function zt(t,n){return{kind:"schema",type:"custom",reference:zt,expects:"unknown",async:!0,check:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return await this.check(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function Gt(t){return{kind:"schema",type:"date",reference:Gt,expects:"Date",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof Date?isNaN(n.value)?r(this,"type",n,e,{received:'"Invalid Date"'}):n.typed=!0:r(this,"type",n,e),n}}}function Ut(t,n){let e=[];for(let s in t)(`${+s}`!==s||typeof t[s]!="string"||!Object.is(t[t[s]],+s))&&e.push(t[s]);return{kind:"schema",type:"enum",reference:Ut,expects:k(e.map(f),"|"),async:!1,enum:t,options:e,message:n,get"~standard"(){return p(this)},"~run"(s,u){return this.options.includes(s.value)?s.typed=!0:r(this,"type",s,u),s}}}function Xt(t,n){return{kind:"schema",type:"exact_optional",reference:Xt,expects:t.expects,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return this.wrapped["~run"](e,s)}}}function Ht(t,n){return{kind:"schema",type:"exact_optional",reference:Ht,expects:t.expects,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return this.wrapped["~run"](e,s)}}}function Jt(t){return{kind:"schema",type:"file",reference:Jt,expects:"File",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof File?n.typed=!0:r(this,"type",n,e),n}}}function Zt(t){return{kind:"schema",type:"function",reference:Zt,expects:"Function",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="function"?n.typed=!0:r(this,"type",n,e),n}}}function Qt(t,n){return{kind:"schema",type:"instance",reference:Qt,expects:t.name,async:!1,class:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value instanceof this.class?e.typed=!0:r(this,"type",e,s),e}}}function R(t,n){if(typeof t==typeof n){if(t===n||t instanceof Date&&n instanceof Date&&+t==+n)return{value:t};if(t&&n&&t.constructor===Object&&n.constructor===Object){for(let e in n)if(e in t){let s=R(t[e],n[e]);if(s.issue)return s;t[e]=s.value}else t[e]=n[e];return{value:t}}if(Array.isArray(t)&&Array.isArray(n)&&t.length===n.length){for(let e=0;e<t.length;e++){let s=R(t[e],n[e]);if(s.issue)return s;t[e]=s.value}return{value:t}}}return{issue:!0}}function Yt(t,n){return{kind:"schema",type:"intersect",reference:Yt,expects:k(t.map(e=>e.expects),"&"),async:!1,options:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){if(this.options.length){let u=e.value,o;e.typed=!0;for(let a of this.options){let I=a["~run"]({value:u},s);if(I.issues&&(e.issues?e.issues.push(...I.issues):e.issues=I.issues,s.abortEarly)){e.typed=!1;break}I.typed||(e.typed=!1),e.typed&&(o?o.push(I.value):o=[I.value])}if(e.typed){e.value=o[0];for(let a=1;a<o.length;a++){let I=R(e.value,o[a]);if(I.issue){r(this,"type",e,s,{received:"unknown"});break}e.value=I.value}}}else r(this,"type",e,s);return e}}}function es(t,n){return{kind:"schema",type:"intersect",reference:es,expects:k(t.map(e=>e.expects),"&"),async:!0,options:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){if(this.options.length){let u=e.value,o;e.typed=!0;let a=await Promise.all(this.options.map(I=>I["~run"]({value:u},s)));for(let I of a){if(I.issues&&(e.issues?e.issues.push(...I.issues):e.issues=I.issues,s.abortEarly)){e.typed=!1;break}I.typed||(e.typed=!1),e.typed&&(o?o.push(I.value):o=[I.value])}if(e.typed){e.value=o[0];for(let I=1;I<o.length;I++){let i=R(e.value,o[I]);if(i.issue){r(this,"type",e,s,{received:"unknown"});break}e.value=i.value}}}else r(this,"type",e,s);return e}}}function ns(t){return{kind:"schema",type:"lazy",reference:ns,expects:"unknown",async:!1,getter:t,get"~standard"(){return p(this)},"~run"(n,e){return this.getter(n.value)["~run"](n,e)}}}function ts(t){return{kind:"schema",type:"lazy",reference:ts,expects:"unknown",async:!0,getter:t,get"~standard"(){return p(this)},async"~run"(n,e){return(await this.getter(n.value))["~run"](n,e)}}}function ss(t,n){return{kind:"schema",type:"literal",reference:ss,expects:f(t),async:!1,literal:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===this.literal?e.typed=!0:r(this,"type",e,s),e}}}function us(t,n){return{kind:"schema",type:"loose_object",reference:us,expects:"Object",async:!1,entries:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let a=this.entries[o];if(o in u||(a.type==="exact_optional"||a.type==="optional"||a.type==="nullish")&&a.default!==void 0){let I=o in u?u[o]:y(a),i=a["~run"]({value:I},s);if(i.issues){let m={type:"object",origin:"value",input:u,key:o,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value[o]=i.value}else if(a.fallback!==void 0)e.value[o]=l(a);else if(a.type!=="exact_optional"&&a.type!=="optional"&&a.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${o}"`,path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]}),s.abortEarly))break}if(!e.issues||!s.abortEarly)for(let o in u)g(u,o)&&!(o in this.entries)&&(e.value[o]=u[o])}else r(this,"type",e,s);return e}}}function rs(t,n){return{kind:"schema",type:"loose_object",reference:rs,expects:"Object",async:!0,entries:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([a,I])=>{if(a in u||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in u?u[a]:await y(I);return[a,i,I,await I["~run"]({value:i},s)]}return[a,u[a],I,null]}));for(let[a,I,i,m]of o)if(m){if(m.issues){let c={type:"object",origin:"value",input:u,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],e.issues?.push(T);if(e.issues||(e.issues=m.issues),s.abortEarly){e.typed=!1;break}}m.typed||(e.typed=!1),e.value[a]=m.value}else if(i.fallback!==void 0)e.value[a]=await l(i);else if(i.type!=="exact_optional"&&i.type!=="optional"&&i.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:u,key:a,value:I}]}),s.abortEarly))break;if(!e.issues||!s.abortEarly)for(let a in u)g(u,a)&&!(a in this.entries)&&(e.value[a]=u[a])}else r(this,"type",e,s);return e}}}function os(t,n){return{kind:"schema",type:"loose_tuple",reference:os,expects:"Array",async:!1,items:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let a=u[o],I=this.items[o]["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}if(!e.issues||!s.abortEarly)for(let o=this.items.length;o<u.length;o++)e.value.push(u[o])}else r(this,"type",e,s);return e}}}function as(t,n){return{kind:"schema",type:"loose_tuple",reference:as,expects:"Array",async:!0,items:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(a,I)=>{let i=u[I];return[I,i,await a["~run"]({value:i},s)]}));for(let[a,I,i]of o){if(i.issues){let m={type:"array",origin:"value",input:u,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value.push(i.value)}if(!e.issues||!s.abortEarly)for(let a=this.items.length;a<u.length;a++)e.value.push(u[a])}else r(this,"type",e,s);return e}}}function is(t,n,e){return{kind:"schema",type:"map",reference:is,expects:"Map",async:!1,key:t,value:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o instanceof Map){s.typed=!0,s.value=new Map;for(let[a,I]of o){let i=this.key["~run"]({value:a},u);if(i.issues){let c={type:"map",origin:"key",input:o,key:a,value:I};for(let T of i.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}let m=this.value["~run"]({value:I},u);if(m.issues){let c={type:"map",origin:"value",input:o,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}(!i.typed||!m.typed)&&(s.typed=!1),s.value.set(i.value,m.value)}}else r(this,"type",s,u);return s}}}function Is(t,n,e){return{kind:"schema",type:"map",reference:Is,expects:"Map",async:!0,key:t,value:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o instanceof Map){s.typed=!0,s.value=new Map;let a=await Promise.all([...o].map(([I,i])=>Promise.all([I,i,this.key["~run"]({value:I},u),this.value["~run"]({value:i},u)])));for(let[I,i,m,c]of a){if(m.issues){let T={type:"map",origin:"key",input:o,key:I,value:i};for(let d of m.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}if(c.issues){let T={type:"map",origin:"value",input:o,key:I,value:i};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}(!m.typed||!c.typed)&&(s.typed=!1),s.value.set(m.value,c.value)}}else r(this,"type",s,u);return s}}}function ps(t){return{kind:"schema",type:"nan",reference:ps,expects:"NaN",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return Number.isNaN(n.value)?n.typed=!0:r(this,"type",n,e),n}}}function ms(t){return{kind:"schema",type:"never",reference:ms,expects:"never",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return r(this,"type",n,e),n}}}function cs(t,n){return{kind:"schema",type:"non_nullable",reference:cs,expects:"!null",async:!1,wrapped:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value!==null&&(e=this.wrapped["~run"](e,s)),e.value===null&&r(this,"type",e,s),e}}}function Ts(t,n){return{kind:"schema",type:"non_nullable",reference:Ts,expects:"!null",async:!0,wrapped:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value!==null&&(e=await this.wrapped["~run"](e,s)),e.value===null&&r(this,"type",e,s),e}}}function ds(t,n){return{kind:"schema",type:"non_nullish",reference:ds,expects:"(!null & !undefined)",async:!1,wrapped:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===null||e.value===void 0||(e=this.wrapped["~run"](e,s)),(e.value===null||e.value===void 0)&&r(this,"type",e,s),e}}}function fs(t,n){return{kind:"schema",type:"non_nullish",reference:fs,expects:"(!null & !undefined)",async:!0,wrapped:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===null||e.value===void 0||(e=await this.wrapped["~run"](e,s)),(e.value===null||e.value===void 0)&&r(this,"type",e,s),e}}}function L(t,n){return{kind:"schema",type:"non_optional",reference:L,expects:"!undefined",async:!1,wrapped:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value!==void 0&&(e=this.wrapped["~run"](e,s)),e.value===void 0&&r(this,"type",e,s),e}}}function K(t,n){return{kind:"schema",type:"non_optional",reference:K,expects:"!undefined",async:!0,wrapped:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value!==void 0&&(e=await this.wrapped["~run"](e,s)),e.value===void 0&&r(this,"type",e,s),e}}}function ys(t){return{kind:"schema",type:"null",reference:ys,expects:"null",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value===null?n.typed=!0:r(this,"type",n,e),n}}}function ls(t,n){return{kind:"schema",type:"nullable",reference:ls,expects:`(${t.expects} | null)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===null&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===null)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function hs(t,n){return{kind:"schema",type:"nullable",reference:hs,expects:`(${t.expects} | null)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===null&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===null)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function ks(t,n){return{kind:"schema",type:"nullish",reference:ks,expects:`(${t.expects} | null | undefined)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return(e.value===null||e.value===void 0)&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===null||e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function xs(t,n){return{kind:"schema",type:"nullish",reference:xs,expects:`(${t.expects} | null | undefined)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return(e.value===null||e.value===void 0)&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===null||e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function Os(t){return{kind:"schema",type:"number",reference:Os,expects:"number",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="number"&&!isNaN(n.value)?n.typed=!0:r(this,"type",n,e),n}}}function gs(t,n){return{kind:"schema",type:"object",reference:gs,expects:"Object",async:!1,entries:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let a=this.entries[o];if(o in u||(a.type==="exact_optional"||a.type==="optional"||a.type==="nullish")&&a.default!==void 0){let I=o in u?u[o]:y(a),i=a["~run"]({value:I},s);if(i.issues){let m={type:"object",origin:"value",input:u,key:o,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value[o]=i.value}else if(a.fallback!==void 0)e.value[o]=l(a);else if(a.type!=="exact_optional"&&a.type!=="optional"&&a.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${o}"`,path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]}),s.abortEarly))break}}else r(this,"type",e,s);return e}}}function ws(t,n){return{kind:"schema",type:"object",reference:ws,expects:"Object",async:!0,entries:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([a,I])=>{if(a in u||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in u?u[a]:await y(I);return[a,i,I,await I["~run"]({value:i},s)]}return[a,u[a],I,null]}));for(let[a,I,i,m]of o)if(m){if(m.issues){let c={type:"object",origin:"value",input:u,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],e.issues?.push(T);if(e.issues||(e.issues=m.issues),s.abortEarly){e.typed=!1;break}}m.typed||(e.typed=!1),e.value[a]=m.value}else if(i.fallback!==void 0)e.value[a]=await l(i);else if(i.type!=="exact_optional"&&i.type!=="optional"&&i.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:u,key:a,value:I}]}),s.abortEarly))break}else r(this,"type",e,s);return e}}}function Ss(t,n,e){return{kind:"schema",type:"object_with_rest",reference:Ss,expects:"Object",async:!1,entries:t,rest:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};for(let a in this.entries){let I=this.entries[a];if(a in o||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in o?o[a]:y(I),m=I["~run"]({value:i},u);if(m.issues){let c={type:"object",origin:"value",input:o,key:a,value:i};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}m.typed||(s.typed=!1),s.value[a]=m.value}else if(I.fallback!==void 0)s.value[a]=l(I);else if(I.type!=="exact_optional"&&I.type!=="optional"&&I.type!=="nullish"&&(r(this,"key",s,u,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:o,key:a,value:o[a]}]}),u.abortEarly))break}if(!s.issues||!u.abortEarly){for(let a in o)if(g(o,a)&&!(a in this.entries)){let I=this.rest["~run"]({value:o[a]},u);if(I.issues){let i={type:"object",origin:"value",input:o,key:a,value:o[a]};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],s.issues?.push(m);if(s.issues||(s.issues=I.issues),u.abortEarly){s.typed=!1;break}}I.typed||(s.typed=!1),s.value[a]=I.value}}}else r(this,"type",s,u);return s}}}function Bs(t,n,e){return{kind:"schema",type:"object_with_rest",reference:Bs,expects:"Object",async:!0,entries:t,rest:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};let[a,I]=await Promise.all([Promise.all(Object.entries(this.entries).map(async([i,m])=>{if(i in o||(m.type==="exact_optional"||m.type==="optional"||m.type==="nullish")&&m.default!==void 0){let c=i in o?o[i]:await y(m);return[i,c,m,await m["~run"]({value:c},u)]}return[i,o[i],m,null]})),Promise.all(Object.entries(o).filter(([i])=>g(o,i)&&!(i in this.entries)).map(async([i,m])=>[i,m,await this.rest["~run"]({value:m},u)]))]);for(let[i,m,c,T]of a)if(T){if(T.issues){let d={type:"object",origin:"value",input:o,key:i,value:m};for(let x of T.issues)x.path?x.path.unshift(d):x.path=[d],s.issues?.push(x);if(s.issues||(s.issues=T.issues),u.abortEarly){s.typed=!1;break}}T.typed||(s.typed=!1),s.value[i]=T.value}else if(c.fallback!==void 0)s.value[i]=await l(c);else if(c.type!=="exact_optional"&&c.type!=="optional"&&c.type!=="nullish"&&(r(this,"key",s,u,{input:void 0,expected:`"${i}"`,path:[{type:"object",origin:"key",input:o,key:i,value:m}]}),u.abortEarly))break;if(!s.issues||!u.abortEarly)for(let[i,m,c]of I){if(c.issues){let T={type:"object",origin:"value",input:o,key:i,value:m};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}c.typed||(s.typed=!1),s.value[i]=c.value}}else r(this,"type",s,u);return s}}}function $(t,n){return{kind:"schema",type:"optional",reference:$,expects:`(${t.expects} | undefined)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function F(t,n){return{kind:"schema",type:"optional",reference:F,expects:`(${t.expects} | undefined)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function z(t,n){return{kind:"schema",type:"picklist",reference:z,expects:k(t.map(f),"|"),async:!1,options:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return this.options.includes(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function As(t){return{kind:"schema",type:"promise",reference:As,expects:"Promise",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof Promise?n.typed=!0:r(this,"type",n,e),n}}}function bs(t,n,e){return{kind:"schema",type:"record",reference:bs,expects:"Object",async:!1,key:t,value:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};for(let a in o)if(g(o,a)){let I=o[a],i=this.key["~run"]({value:a},u);if(i.issues){let c={type:"object",origin:"key",input:o,key:a,value:I};for(let T of i.issues)T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}let m=this.value["~run"]({value:I},u);if(m.issues){let c={type:"object",origin:"value",input:o,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}(!i.typed||!m.typed)&&(s.typed=!1),i.typed&&(s.value[i.value]=m.value)}}else r(this,"type",s,u);return s}}}function Es(t,n,e){return{kind:"schema",type:"record",reference:Es,expects:"Object",async:!0,key:t,value:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};let a=await Promise.all(Object.entries(o).filter(([I])=>g(o,I)).map(([I,i])=>Promise.all([I,i,this.key["~run"]({value:I},u),this.value["~run"]({value:i},u)])));for(let[I,i,m,c]of a){if(m.issues){let T={type:"object",origin:"key",input:o,key:I,value:i};for(let d of m.issues)d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}if(c.issues){let T={type:"object",origin:"value",input:o,key:I,value:i};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}(!m.typed||!c.typed)&&(s.typed=!1),m.typed&&(s.value[m.value]=c.value)}}else r(this,"type",s,u);return s}}}function Ms(t,n){return{kind:"schema",type:"set",reference:Ms,expects:"Set",async:!1,value:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u instanceof Set){e.typed=!0,e.value=new Set;for(let o of u){let a=this.value["~run"]({value:o},s);if(a.issues){let I={type:"set",origin:"value",input:u,key:null,value:o};for(let i of a.issues)i.path?i.path.unshift(I):i.path=[I],e.issues?.push(i);if(e.issues||(e.issues=a.issues),s.abortEarly){e.typed=!1;break}}a.typed||(e.typed=!1),e.value.add(a.value)}}else r(this,"type",e,s);return e}}}function Ps(t,n){return{kind:"schema",type:"set",reference:Ps,expects:"Set",async:!0,value:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u instanceof Set){e.typed=!0,e.value=new Set;let o=await Promise.all([...u].map(async a=>[a,await this.value["~run"]({value:a},s)]));for(let[a,I]of o){if(I.issues){let i={type:"set",origin:"value",input:u,key:null,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.add(I.value)}}else r(this,"type",e,s);return e}}}function Rs(t,n){return{kind:"schema",type:"strict_object",reference:Rs,expects:"Object",async:!1,entries:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let a=this.entries[o];if(o in u||(a.type==="exact_optional"||a.type==="optional"||a.type==="nullish")&&a.default!==void 0){let I=o in u?u[o]:y(a),i=a["~run"]({value:I},s);if(i.issues){let m={type:"object",origin:"value",input:u,key:o,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value[o]=i.value}else if(a.fallback!==void 0)e.value[o]=l(a);else if(a.type!=="exact_optional"&&a.type!=="optional"&&a.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${o}"`,path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]}),s.abortEarly))break}if(!e.issues||!s.abortEarly){for(let o in u)if(!(o in this.entries)){r(this,"key",e,s,{input:o,expected:"never",path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]});break}}}else r(this,"type",e,s);return e}}}function js(t,n){return{kind:"schema",type:"strict_object",reference:js,expects:"Object",async:!0,entries:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([a,I])=>{if(a in u||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in u?u[a]:await y(I);return[a,i,I,await I["~run"]({value:i},s)]}return[a,u[a],I,null]}));for(let[a,I,i,m]of o)if(m){if(m.issues){let c={type:"object",origin:"value",input:u,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],e.issues?.push(T);if(e.issues||(e.issues=m.issues),s.abortEarly){e.typed=!1;break}}m.typed||(e.typed=!1),e.value[a]=m.value}else if(i.fallback!==void 0)e.value[a]=await l(i);else if(i.type!=="exact_optional"&&i.type!=="optional"&&i.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:u,key:a,value:I}]}),s.abortEarly))break;if(!e.issues||!s.abortEarly){for(let a in u)if(!(a in this.entries)){r(this,"key",e,s,{input:a,expected:"never",path:[{type:"object",origin:"key",input:u,key:a,value:u[a]}]});break}}}else r(this,"type",e,s);return e}}}function vs(t,n){return{kind:"schema",type:"strict_tuple",reference:vs,expects:"Array",async:!1,items:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let a=u[o],I=this.items[o]["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}!(e.issues&&s.abortEarly)&&this.items.length<u.length&&r(this,"type",e,s,{input:u[this.items.length],expected:"never",path:[{type:"array",origin:"value",input:u,key:this.items.length,value:u[this.items.length]}]})}else r(this,"type",e,s);return e}}}function qs(t,n){return{kind:"schema",type:"strict_tuple",reference:qs,expects:"Array",async:!0,items:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(a,I)=>{let i=u[I];return[I,i,await a["~run"]({value:i},s)]}));for(let[a,I,i]of o){if(i.issues){let m={type:"array",origin:"value",input:u,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value.push(i.value)}!(e.issues&&s.abortEarly)&&this.items.length<u.length&&r(this,"type",e,s,{input:u[this.items.length],expected:"never",path:[{type:"array",origin:"value",input:u,key:this.items.length,value:u[this.items.length]}]})}else r(this,"type",e,s);return e}}}function _s(t){return{kind:"schema",type:"string",reference:_s,expects:"string",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="string"?n.typed=!0:r(this,"type",n,e),n}}}function Ds(t){return{kind:"schema",type:"symbol",reference:Ds,expects:"symbol",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="symbol"?n.typed=!0:r(this,"type",n,e),n}}}function Ws(t,n){return{kind:"schema",type:"tuple",reference:Ws,expects:"Array",async:!1,items:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let a=u[o],I=this.items[o]["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}}else r(this,"type",e,s);return e}}}function Vs(t,n){return{kind:"schema",type:"tuple",reference:Vs,expects:"Array",async:!0,items:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(a,I)=>{let i=u[I];return[I,i,await a["~run"]({value:i},s)]}));for(let[a,I,i]of o){if(i.issues){let m={type:"array",origin:"value",input:u,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value.push(i.value)}}else r(this,"type",e,s);return e}}}function Ns(t,n,e){return{kind:"schema",type:"tuple_with_rest",reference:Ns,expects:"Array",async:!1,items:t,rest:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(Array.isArray(o)){s.typed=!0,s.value=[];for(let a=0;a<this.items.length;a++){let I=o[a],i=this.items[a]["~run"]({value:I},u);if(i.issues){let m={type:"array",origin:"value",input:o,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],s.issues?.push(c);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}i.typed||(s.typed=!1),s.value.push(i.value)}if(!s.issues||!u.abortEarly)for(let a=this.items.length;a<o.length;a++){let I=o[a],i=this.rest["~run"]({value:I},u);if(i.issues){let m={type:"array",origin:"value",input:o,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],s.issues?.push(c);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}i.typed||(s.typed=!1),s.value.push(i.value)}}else r(this,"type",s,u);return s}}}function Cs(t,n,e){return{kind:"schema",type:"tuple_with_rest",reference:Cs,expects:"Array",async:!0,items:t,rest:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(Array.isArray(o)){s.typed=!0,s.value=[];let[a,I]=await Promise.all([Promise.all(this.items.map(async(i,m)=>{let c=o[m];return[m,c,await i["~run"]({value:c},u)]})),Promise.all(o.slice(this.items.length).map(async(i,m)=>[m+this.items.length,i,await this.rest["~run"]({value:i},u)]))]);for(let[i,m,c]of a){if(c.issues){let T={type:"array",origin:"value",input:o,key:i,value:m};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}c.typed||(s.typed=!1),s.value.push(c.value)}if(!s.issues||!u.abortEarly)for(let[i,m,c]of I){if(c.issues){let T={type:"array",origin:"value",input:o,key:i,value:m};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}c.typed||(s.typed=!1),s.value.push(c.value)}}else r(this,"type",s,u);return s}}}function Ls(t){return{kind:"schema",type:"undefined",reference:Ls,expects:"undefined",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value===void 0?n.typed=!0:r(this,"type",n,e),n}}}function Ks(t,n){return{kind:"schema",type:"undefinedable",reference:Ks,expects:`(${t.expects} | undefined)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function $s(t,n){return{kind:"schema",type:"undefinedable",reference:$s,expects:`(${t.expects} | undefined)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function j(t){let n;if(t)for(let e of t)n?n.push(...e.issues):n=e.issues;return n}function Fs(t,n){return{kind:"schema",type:"union",reference:Fs,expects:k(t.map(e=>e.expects),"|"),async:!1,options:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u,o,a;for(let I of this.options){let i=I["~run"]({value:e.value},s);if(i.typed)if(i.issues)o?o.push(i):o=[i];else{u=i;break}else a?a.push(i):a=[i]}if(u)return u;if(o){if(o.length===1)return o[0];r(this,"type",e,s,{issues:j(o)}),e.typed=!0}else{if(a?.length===1)return a[0];r(this,"type",e,s,{issues:j(a)})}return e}}}function zs(t,n){return{kind:"schema",type:"union",reference:zs,expects:k(t.map(e=>e.expects),"|"),async:!0,options:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u,o,a;for(let I of this.options){let i=await I["~run"]({value:e.value},s);if(i.typed)if(i.issues)o?o.push(i):o=[i];else{u=i;break}else a?a.push(i):a=[i]}if(u)return u;if(o){if(o.length===1)return o[0];r(this,"type",e,s,{issues:j(o)}),e.typed=!0}else{if(a?.length===1)return a[0];r(this,"type",e,s,{issues:j(a)})}return e}}}function Gs(){return{kind:"schema",type:"unknown",reference:Gs,expects:"unknown",async:!1,get"~standard"(){return p(this)},"~run"(t){return t.typed=!0,t}}}function Us(t,n,e){return{kind:"schema",type:"variant",reference:Us,expects:"Object",async:!1,key:t,options:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){let a,I=0,i=this.key,m=[],c=(T,d)=>{for(let x of T.options){if(x.type==="variant")c(x,new Set(d).add(x.key));else{let v=!0,S=0;for(let h of d){let B=x.entries[h];if(h in o?B["~run"]({typed:!1,value:o[h]},u).issues:B.type!=="exact_optional"&&B.type!=="optional"&&B.type!=="nullish"){v=!1,i!==h&&(I<S||I===S&&h in o&&!(i in o))&&(I=S,i=h,m=[]),i===h&&m.push(x.entries[h].expects);break}S++}if(v){let h=x["~run"]({value:o},u);(!a||!a.typed&&h.typed)&&(a=h)}}if(a&&!a.issues)break}};if(c(this,new Set([this.key])),a)return a;r(this,"type",s,u,{input:o[i],expected:k(m,"|"),path:[{type:"object",origin:"value",input:o,key:i,value:o[i]}]})}else r(this,"type",s,u);return s}}}function Xs(t,n,e){return{kind:"schema",type:"variant",reference:Xs,expects:"Object",async:!0,key:t,options:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){let a,I=0,i=this.key,m=[],c=async(T,d)=>{for(let x of T.options){if(x.type==="variant")await c(x,new Set(d).add(x.key));else{let v=!0,S=0;for(let h of d){let B=x.entries[h];if(h in o?(await B["~run"]({typed:!1,value:o[h]},u)).issues:B.type!=="exact_optional"&&B.type!=="optional"&&B.type!=="nullish"){v=!1,i!==h&&(I<S||I===S&&h in o&&!(i in o))&&(I=S,i=h,m=[]),i===h&&m.push(x.entries[h].expects);break}S++}if(v){let h=await x["~run"]({value:o},u);(!a||!a.typed&&h.typed)&&(a=h)}}if(a&&!a.issues)break}};if(await c(this,new Set([this.key])),a)return a;r(this,"type",s,u,{input:o[i],expected:k(m,"|"),path:[{type:"object",origin:"value",input:o,key:i,value:o[i]}]})}else r(this,"type",s,u);return s}}}function Hs(t){return{kind:"schema",type:"void",reference:Hs,expects:"void",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value===void 0?n.typed=!0:r(this,"type",n,e),n}}}function bc(t,n){return z(Object.keys(t.entries),n)}function Pc(t,n){let e={...t.entries};for(let s of n)delete e[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function Ee(t,n,e){let s=t["~run"]({value:n},w(e));if(s.issues)throw new O(s.issues);return s.value}async function Me(t,n,e){let s=await t["~run"]({value:n},w(e));if(s.issues)throw new O(s.issues);return s.value}function Nc(t,n){let e=s=>Ee(t,s,n);return e.schema=t,e.config=n,e}function Kc(t,n){let e=s=>Me(t,s,n);return e.schema=t,e.config=n,e}function Gc(t,n){let e={};for(let s in t.entries)e[s]=!n||n.includes(s)?$(t.entries[s]):t.entries[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function Jc(t,n){let e={};for(let s in t.entries)e[s]=!n||n.includes(s)?F(t.entries[s]):t.entries[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function Yc(t,n){let e={};for(let s of n)e[s]=t.entries[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function tT(...t){return{...t[0],pipe:t,get"~standard"(){return p(this)},"~run"(n,e){for(let s of t)if(s.kind!=="metadata"){if(n.issues&&(s.kind==="schema"||s.kind==="transformation")){n.typed=!1;break}(!n.issues||!e.abortEarly&&!e.abortPipeEarly)&&(n=s["~run"](n,e))}return n}}}function rT(...t){return{...t[0],pipe:t,async:!0,get"~standard"(){return p(this)},async"~run"(n,e){for(let s of t)if(s.kind!=="metadata"){if(n.issues&&(s.kind==="schema"||s.kind==="transformation")){n.typed=!1;break}(!n.issues||!e.abortEarly&&!e.abortPipeEarly)&&(n=await s["~run"](n,e))}return n}}}function IT(t,n,e){let s=Array.isArray(n)?n:void 0,u=Array.isArray(n)?e:n,o={};for(let a in t.entries)o[a]=!s||s.includes(a)?L(t.entries[a],u):t.entries[a];return{...t,entries:o,get"~standard"(){return p(this)}}}function TT(t,n,e){let s=Array.isArray(n)?n:void 0,u=Array.isArray(n)?e:n,o={};for(let a in t.entries)o[a]=!s||s.includes(a)?K(t.entries[a],u):t.entries[a];return{...t,entries:o,get"~standard"(){return p(this)}}}function Pe(t,n,e){let s=t["~run"]({value:n},w(e));return{typed:s.typed,success:!s.issues,output:s.value,issues:s.issues}}async function Re(t,n,e){let s=await t["~run"]({value:n},w(e));return{typed:s.typed,success:!s.issues,output:s.value,issues:s.issues}}function xT(t,n){let e=s=>Pe(t,s,n);return e.schema=t,e.config=n,e}function wT(t,n){let e=s=>Re(t,s,n);return e.schema=t,e.config=n,e}function BT(t){return t.wrapped}export{J as BASE64_REGEX,Z as BIC_REGEX,Q as CUID2_REGEX,Y as DECIMAL_REGEX,ee as DIGITS_REGEX,ne as EMAIL_REGEX,te as EMOJI_REGEX,se as HEXADECIMAL_REGEX,ue as HEX_COLOR_REGEX,re as IMEI_REGEX,oe as IPV4_REGEX,ae as IPV6_REGEX,ie as IP_REGEX,Ie as ISO_DATE_REGEX,pe as ISO_DATE_TIME_REGEX,Te as ISO_TIMESTAMP_REGEX,me as ISO_TIME_REGEX,ce as ISO_TIME_SECOND_REGEX,de as ISO_WEEK_REGEX,fe as MAC48_REGEX,ye as MAC64_REGEX,le as MAC_REGEX,he as NANO_ID_REGEX,ke as OCTAL_REGEX,xe as RFC_EMAIL_REGEX,Oe as SLUG_REGEX,ge as ULID_REGEX,we as UUID_REGEX,O as ValiError,r as _addIssue,A as _getByteCount,b as _getGraphemeCount,p as _getStandardProps,E as _getWordCount,W as _isLuhnAlgo,g as _isValidObjectKey,k as _joinExpects,f as _stringify,Vt as any,ve as args,qe as argsAsync,Nt as array,Ct as arrayAsync,Gi as assert,_e as awaitAsync,De as base64,We as bic,Lt as bigint,Kt as blob,$t as boolean,Ve as brand,Ne as bytes,Ce as check,Le as checkAsync,Ke as checkItems,$e as checkItemsAsync,Hi as config,Ue as creditCard,Xe as cuid2,Ft as custom,zt as customAsync,Gt as date,He as decimal,Zs as deleteGlobalConfig,eu as deleteGlobalMessage,su as deleteSchemaMessage,ou as deleteSpecificMessage,Je as description,Ze as digits,Qe as email,Ye as emoji,en as empty,nn as endsWith,xu as entriesFromList,gu as entriesFromObjects,Ut as enum,Ut as enum_,tn as everyItem,Xt as exactOptional,Ht as exactOptionalAsync,sn as excludes,eI as fallback,uI as fallbackAsync,Jt as file,un as filterItems,rn as findItem,on as finite,aI as flatten,II as forward,mI as forwardAsync,Zt as function,Zt as function_,y as getDefault,Se as getDefaults,Be as getDefaultsAsync,H as getDotPath,l as getFallback,Ae as getFallbacks,be as getFallbacksAsync,w as getGlobalConfig,G as getGlobalMessage,U as getSchemaMessage,X as getSpecificMessage,an as graphemes,In as gtValue,mn as hash,Tn as hexColor,cn as hexadecimal,dn as imei,fn as includes,Qt as instance,yn as integer,Yt as intersect,es as intersectAsync,ln as ip,hn as ipv4,kn as ipv6,gI as is,Bu as isOfKind,bu as isOfType,Pu as isValiError,xn as isoDate,On as isoDateTime,gn as isoTime,wn as isoTimeSecond,Sn as isoTimestamp,Bn as isoWeek,bc as keyof,ns as lazy,ts as lazyAsync,An as length,ss as literal,us as looseObject,rs as looseObjectAsync,os as looseTuple,as as looseTupleAsync,bn as ltValue,En as mac,Mn as mac48,Pn as mac64,is as map,Is as mapAsync,Rn as mapItems,jn as maxBytes,vn as maxGraphemes,qn as maxLength,_n as maxSize,Dn as maxValue,Wn as maxWords,Vn as metadata,Nn as mimeType,Cn as minBytes,Ln as minGraphemes,Kn as minLength,$n as minSize,Fn as minValue,zn as minWords,Gn as multipleOf,ps as nan,Un as nanoid,ms as never,Xn as nonEmpty,cs as nonNullable,Ts as nonNullableAsync,ds as nonNullish,fs as nonNullishAsync,L as nonOptional,K as nonOptionalAsync,Hn as normalize,Jn as notBytes,Zn as notGraphemes,Qn as notLength,Yn as notSize,et as notValue,nt as notValues,tt as notWords,ys as null,ys as null_,ls as nullable,hs as nullableAsync,ks as nullish,xs as nullishAsync,Os as number,gs as object,ws as objectAsync,Ss as objectWithRest,Bs as objectWithRestAsync,st as octal,Pc as omit,$ as optional,F as optionalAsync,Ee as parse,Me as parseAsync,Nc as parser,Kc as parserAsync,Gc as partial,Jc as partialAsync,ut as partialCheck,rt as partialCheckAsync,Yc as pick,z as picklist,tT as pipe,rT as pipeAsync,As as promise,ot as rawCheck,at as rawCheckAsync,it as rawTransform,It as rawTransformAsync,pt as readonly,bs as record,Es as recordAsync,mt as reduceItems,ct as regex,IT as required,TT as requiredAsync,Tt as returns,dt as returnsAsync,ft as rfcEmail,yt as safeInteger,Pe as safeParse,Re as safeParseAsync,xT as safeParser,wT as safeParserAsync,Ms as set,Ps as setAsync,Js as setGlobalConfig,Ys as setGlobalMessage,tu as setSchemaMessage,ru as setSpecificMessage,lt as size,ht as slug,kt as someItem,xt as sortItems,Ot as startsWith,Rs as strictObject,js as strictObjectAsync,vs as strictTuple,qs as strictTupleAsync,_s as string,Ds as symbol,gt as title,wt as toLowerCase,St as toMaxValue,Bt as toMinValue,At as toUpperCase,bt as transform,Et as transformAsync,Mt as trim,Pt as trimEnd,Rt as trimStart,Ws as tuple,Vs as tupleAsync,Ns as tupleWithRest,Cs as tupleWithRestAsync,jt as ulid,Ls as undefined,Ls as undefined_,Ks as undefinedable,$s as undefinedableAsync,Fs as union,zs as unionAsync,Gs as unknown,BT as unwrap,vt as url,qt as uuid,_t as value,Dt as values,Us as variant,Xs as variantAsync,Hs as void,Hs as void_,Wt as words};
