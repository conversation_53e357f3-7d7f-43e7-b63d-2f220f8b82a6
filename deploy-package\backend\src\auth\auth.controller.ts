import { <PERSON>, Post, Body, BadRequestException, Get, Param } from '@nestjs/common';
import { AuthService } from './auth.service';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('telegram')
  async authenticateTelegram(@Body() body: { initData: string; user: any }) {
    try {
      const { initData, user } = body;
      
      if (!initData || !user) {
        throw new BadRequestException('Missing initData or user');
      }

      // Проверяем подпись Telegram
      const isValid = await this.authService.verifyTelegramAuth(initData);
      
      if (!isValid) {
        throw new BadRequestException('Invalid Telegram signature');
      }

      // Создаем или обновляем пользователя
      const userData = await this.authService.createOrUpdateUser({
        telegram_id: user.id.toString(),
        username: user.username || 'user',
        firstname: user.first_name || 'Пользователь',
        lastname: user.last_name || '',
        language_code: user.language_code || 'ru'
      });

      return {
        success: true,
        user: userData,
        verified: true
      };
    } catch (error) {
      console.error('Telegram auth error:', error);
      throw new BadRequestException('Authentication failed');
    }
  }
}
