.telegram-auth {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: var(--space-4);
  background: var(--system-background);
}

.auth-container {
  max-width: 400px;
  width: 100%;
  padding: var(--space-6);
  text-align: center;
  border-radius: 24px !important;
}

.auth-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
}

.auth-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: var(--tertiary-system-background);
  border-radius: 20px;
  border: 1px solid var(--separator);
  margin-bottom: var(--space-2);
}

.user-preview {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--tertiary-system-background);
  border-radius: 16px;
  border: 1px solid var(--separator);
  width: 100%;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--secondary-system-background);
  border-radius: 12px;
  border: 1px solid var(--separator);
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--space-1);
}

.fallback-notice {
  padding: var(--space-2);
  background: rgba(255, 159, 10, 0.1);
  border: 1px solid rgba(255, 159, 10, 0.3);
  border-radius: 12px;
  width: 100%;
}

/* Анимации */
@keyframes pulse-auth {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.auth-icon {
  animation: pulse-auth 2s ease-in-out infinite;
}

/* Мобильная адаптивность */
@media (max-width: 480px) {
  .telegram-auth {
    padding: var(--space-3);
  }
  
  .auth-container {
    padding: var(--space-4);
  }
  
  .auth-icon {
    width: 64px;
    height: 64px;
  }
  
  .user-preview {
    flex-direction: column;
    text-align: center;
  }
  
  .user-details {
    align-items: center;
  }
}
