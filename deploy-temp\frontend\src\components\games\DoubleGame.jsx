import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Di<PERSON>,
  Play,
  Coins,
  Loader2,
  TrendingUp,
  Target,
  PartyPopper,
  Frown
} from 'lucide-react';
import ApiService from '../../services/ApiService';
import './DoubleGame.css';

const DoubleGame = ({ user, balance, updateBalance }) => {
  const [betAmount, setBetAmount] = useState('');
  const [selectedMultiplier, setSelectedMultiplier] = useState(null);
  const [loading, setLoading] = useState(false);
  const [gameResult, setGameResult] = useState(null);

  const multipliers = [
    { value: 2, probability: 45, color: '#00ff88' },
    { value: 3, probability: 30, color: '#ffd700' },
    { value: 5, probability: 20, color: '#ff6b6b' },
    { value: 20, probability: 5, color: '#ff4757' },
  ];

  const playGame = async () => {
    if (!betAmount || parseFloat(betAmount) <= 0) {
      alert('Введите корректную сумму ставки');
      return;
    }

    if (parseFloat(betAmount) > balance) {
      alert('Недостаточно средств');
      return;
    }

    if (!selectedMultiplier) {
      alert('Выберите множитель');
      return;
    }

    setLoading(true);
    setGameResult(null);

    try {
      const result = await ApiService.playDouble(
        user.telegram_id,
        parseFloat(betAmount),
        selectedMultiplier
      );

      if (result.success) {
        const newBalance = balance - parseFloat(betAmount) + (result.payout || 0);
        updateBalance(newBalance);
        
        setGameResult({
          won: result.payout > 0,
          resultMultiplier: result.result_multiplier,
          chosenMultiplier: selectedMultiplier,
          payout: result.payout || 0,
          betAmount: parseFloat(betAmount),
        });
      }
    } catch (error) {
      console.error('Error playing double:', error);
      alert('Ошибка при игре');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="double-game">
      <motion.div
        className="game-header card-elevated"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="header-icon">
          <Dices size={48} className="text-danger" />
        </div>
        <h2 className="text-title-1">Double Roulette</h2>
        <p className="text-callout text-secondary-label">Выберите множитель и испытайте удачу в рулетке</p>
        <div className="stats-row">
          <div className="stat-item">
            <Target size={16} className="text-primary" />
            <span className="text-footnote">Макс. выигрыш: 20x</span>
          </div>
          <div className="stat-item">
            <Coins size={16} className="text-success" />
            <span className="text-footnote">Ваш баланс: {balance.toFixed(2)} TON</span>
          </div>
        </div>
      </motion.div>

      <motion.div
        className="multipliers-grid card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <div className="section-header">
          <h3 className="text-title-3">Выберите множитель</h3>
          <div className="section-icon">
            <TrendingUp size={24} className="text-warning" />
          </div>
        </div>
        <div className="multipliers">
          {multipliers.map((mult, index) => (
            <motion.button
              key={mult.value}
              className={`multiplier-btn ${selectedMultiplier === mult.value ? 'selected' : ''}`}
              style={{
                borderColor: mult.color,
                backgroundColor: selectedMultiplier === mult.value ? `${mult.color}20` : 'transparent'
              }}
              onClick={() => setSelectedMultiplier(mult.value)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled={loading}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <div className="multiplier-header">
                <div className="multiplier-value" style={{ color: mult.color }}>
                  {mult.value}x
                </div>
                <div className="multiplier-badge" style={{ backgroundColor: mult.color }}>
                  {mult.probability}%
                </div>
              </div>
              <div className="multiplier-description">
                <span className="text-footnote text-tertiary-label">
                  Шанс выигрыша
                </span>
              </div>
            </motion.button>
          ))}
        </div>
      </motion.div>

      <motion.div
        className="bet-controls card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="section-header">
          <h3 className="text-title-3">Сумма ставки</h3>
          <div className="section-icon">
            <Coins size={24} className="text-success" />
          </div>
        </div>

        <div className="form-group">
          <label className="text-callout font-semibold">Введите сумму (TON)</label>
          <div className="input-group">
            <Coins size={20} className="input-icon text-success" />
            <input
              type="number"
              value={betAmount}
              onChange={(e) => setBetAmount(e.target.value)}
              placeholder="0.00"
              className="input input-with-icon"
              disabled={loading}
              step="0.01"
              min="0.01"
            />
          </div>
          <div className="form-hint">
            <span className="text-footnote text-tertiary-label">
              Минимальная ставка: 0.01 TON • Максимальная: {balance.toFixed(2)} TON
            </span>
          </div>
        </div>

        <div className="quick-bets">
          <span className="text-callout font-semibold">Быстрые ставки:</span>
          <div className="quick-bets-grid">
            {['0.1', '0.5', '1.0', '2.0'].map((amount, index) => (
              <motion.button
                key={amount}
                className="quick-bet-btn"
                onClick={() => setBetAmount(amount)}
                disabled={loading}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                {amount} TON
              </motion.button>
            ))}
          </div>
        </div>

        <button
          className="btn btn-primary btn-lg w-full play-btn"
          onClick={playGame}
          disabled={loading || !betAmount || !selectedMultiplier}
        >
          {loading ? (
            <Loader2 size={20} className="animate-spin" />
          ) : (
            <Play size={20} />
          )}
          {loading ? 'Игра...' : 'Играть'}
        </button>
      </motion.div>

      {gameResult && (
        <motion.div
          className={`game-result card-elevated ${gameResult.won ? 'won' : 'lost'}`}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="result-header">
            <div className="result-icon">
              {gameResult.won ? (
                <PartyPopper size={48} className="text-success" />
              ) : (
                <Frown size={48} className="text-danger" />
              )}
            </div>
            <h3 className={`text-title-2 ${gameResult.won ? 'text-success' : 'text-danger'}`}>
              {gameResult.won ? 'Победа!' : 'Проигрыш!'}
            </h3>
          </div>

          <div className="result-details">
            <div className="result-row">
              <span className="text-callout text-secondary-label">Выпало:</span>
              <span className="text-headline font-semibold">{gameResult.resultMultiplier}x</span>
            </div>
            <div className="result-row">
              <span className="text-callout text-secondary-label">Ваш выбор:</span>
              <span className="text-headline font-semibold">{gameResult.chosenMultiplier}x</span>
            </div>
            <div className="result-row">
              <span className="text-callout text-secondary-label">Ставка:</span>
              <span className="text-headline font-semibold">{gameResult.betAmount.toFixed(2)} TON</span>
            </div>
            {gameResult.won && (
              <div className="result-row payout">
                <span className="text-callout text-secondary-label">Выигрыш:</span>
                <span className="text-headline font-bold text-success">
                  +{gameResult.payout.toFixed(2)} TON
                </span>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default DoubleGame;
