# Multi-stage build
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Copy package files
COPY frontend/package*.json ./frontend/
COPY backend/package*.json ./backend/

# Install dependencies
RUN cd frontend && npm ci --only=production
RUN cd backend && npm ci --only=production

# Build frontend
FROM base AS frontend-builder
WORKDIR /app
COPY frontend/ ./frontend/
COPY --from=deps /app/frontend/node_modules ./frontend/node_modules
RUN cd frontend && npm run build

# Build backend
FROM base AS backend-builder
WORKDIR /app
COPY backend/ ./backend/
COPY --from=deps /app/backend/node_modules ./backend/node_modules
RUN cd backend && npm run build

# Production image
FROM base AS runner
WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nestjs

# Copy built applications
COPY --from=backend-builder --chown=nestjs:nodejs /app/backend/dist ./backend/dist
COPY --from=backend-builder --chown=nestjs:nodejs /app/backend/node_modules ./backend/node_modules
COPY --from=backend-builder --chown=nestjs:nodejs /app/backend/package.json ./backend/package.json

COPY --from=frontend-builder --chown=nestjs:nodejs /app/frontend/dist ./frontend/dist

# Copy static files to backend public directory
RUN mkdir -p ./backend/public
COPY --from=frontend-builder --chown=nestjs:nodejs /app/frontend/dist/* ./backend/public/

USER nestjs

EXPOSE 3000

ENV NODE_ENV=production
ENV PORT=3000

CMD ["node", "backend/dist/main"]
