"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../users/user.entity");
const crypto = require("crypto");
let AuthService = class AuthService {
    userRepository;
    constructor(userRepository) {
        this.userRepository = userRepository;
    }
    async verifyTelegramAuth(initData) {
        try {
            const botToken = process.env.TELEGRAM_BOT_TOKEN;
            if (!botToken) {
                console.warn('TELEGRAM_BOT_TOKEN not set, skipping verification');
                return true;
            }
            const secret = crypto.createHash('sha256').update(botToken).digest();
            const parsed = new URLSearchParams(initData);
            const hash = parsed.get('hash');
            parsed.delete('hash');
            const dataCheckString = [...parsed.entries()]
                .sort(([a], [b]) => a.localeCompare(b))
                .map(([k, v]) => `${k}=${v}`)
                .join('\n');
            const hmac = crypto.createHmac('sha256', secret).update(dataCheckString).digest('hex');
            return hmac === hash;
        }
        catch (error) {
            console.error('Error verifying Telegram auth:', error);
            return false;
        }
    }
    async createOrUpdateUser(userData) {
        try {
            let user = await this.userRepository.findOne({
                where: { telegram_id: userData.telegram_id }
            });
            if (user) {
                user.username = userData.username;
                user.firstname = userData.firstname;
                if (userData.lastname)
                    user.lastname = userData.lastname;
                if (userData.language_code)
                    user.language_code = userData.language_code;
                user.updated_at = new Date();
                await this.userRepository.save(user);
            }
            else {
                user = this.userRepository.create({
                    id: userData.telegram_id,
                    telegram_id: userData.telegram_id,
                    username: userData.username,
                    firstname: userData.firstname,
                    lastname: userData.lastname || '',
                    language_code: userData.language_code || 'ru',
                    balance_ton: 0,
                });
                await this.userRepository.save(user);
            }
            return user;
        }
        catch (error) {
            console.error('Error creating/updating user:', error);
            throw error;
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AuthService);
//# sourceMappingURL=auth.service.js.map