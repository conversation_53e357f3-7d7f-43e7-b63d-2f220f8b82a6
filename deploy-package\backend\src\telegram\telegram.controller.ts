import { Controller, Get, Param, HttpException, HttpStatus } from '@nestjs/common';
import { TelegramService } from './telegram.service';

@Controller('telegram')
export class TelegramController {
  constructor(private readonly telegramService: TelegramService) {}

  @Get('user-photo/:userId')
  async getUserPhoto(@Param('userId') userId: string) {
    try {
      const photoUrl = await this.telegramService.getUserProfilePhoto(userId);
      return { photo_url: photoUrl };
    } catch (error) {
      console.error('Error getting user photo:', error);
      throw new HttpException('Failed to get user photo', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
