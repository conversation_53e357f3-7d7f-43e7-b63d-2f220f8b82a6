# 🚀 Финальные инструкции по деплою PEPE CAS

## ✅ Что готово:
- 📦 Файлы подготовлены в папке `deploy-package`
- 🤖 Telegram бот токен настроен
- 🔧 Скрипты автоматической установки созданы

## 🎯 Следующие шаги (выполните по порядку):

### 1. Загрузка файлов на сервер
Используйте любой SSH/SFTP клиент:

**Вариант A: Командная строка (если есть SSH)**
```bash
scp -r deploy-package/* sexuz@*************:~/
```

**Вариант B: WinSCP/FileZilla**
- Host: `*************`
- Username: `sexuz`
- Port: `22`
- Загрузите все файлы из папки `deploy-package`

### 2. Подключение к серверу и запуск
```bash
# Подключитесь к серверу
ssh sexuz@*************

# Запустите полную установку
chmod +x full-setup.sh
./full-setup.sh
```

### 3. Проверка результата
После выполнения скрипта:
- ✅ Приложение будет доступно по адресу: `https://*************`
- ✅ Telegram бот будет настроен с кнопкой "🎰 Играть"
- ✅ SSL сертификат будет установлен

### 4. Тестирование
1. Откройте `https://*************` в браузере
2. Найдите вашего бота в Telegram
3. Нажмите кнопку "🎰 Играть"
4. Приложение должно открыться в Telegram

## 🔧 Если что-то пошло не так:

### Проверка статуса сервисов:
```bash
# Проверить приложение
pm2 status
pm2 logs pepe-cas

# Проверить Nginx
sudo systemctl status nginx
sudo nginx -t

# Проверить порты
sudo netstat -tlnp | grep :3000
sudo netstat -tlnp | grep :443
```

### Перезапуск сервисов:
```bash
# Перезапуск приложения
pm2 restart pepe-cas

# Перезапуск Nginx
sudo systemctl restart nginx
```

### Просмотр логов:
```bash
# Логи приложения
pm2 logs pepe-cas

# Логи Nginx
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log
```

## 📱 Настройка бота (если нужно изменить):

```bash
# Изменить URL Web App
curl -X POST "https://api.telegram.org/bot7918026591:AAHrRX3Z1tLtO7aE_gPblArhJMGTv5kixvI/setChatMenuButton" \
-H "Content-Type: application/json" \
-d '{
  "menu_button": {
    "type": "web_app",
    "text": "🎰 Играть",
    "web_app": {
      "url": "https://*************"
    }
  }
}'
```

## 🎉 После успешного деплоя:

Ваше PEPE CAS мини-приложение будет полностью функциональным:
- 🔐 Автоматическая авторизация через Telegram
- 🎮 Все игры работают
- 💰 Интеграция с TON
- 📊 Лидерборд и статистика
- 🎨 Красивый фиолетовый дизайн

**Готово к использованию! 🚀**
