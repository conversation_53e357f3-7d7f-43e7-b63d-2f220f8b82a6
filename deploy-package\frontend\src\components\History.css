/* History Component Styles */
.history {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.history-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 16px;
  color: rgba(255, 255, 255, 0.7);
}

/* Header */
.history-header {
  text-align: center;
  margin-bottom: 10px;
}

.history-header h1 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 24px;
  margin-bottom: 8px;
  color: #fff;
}

.history-header p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

/* Filters */
.history-filters {
  padding: 16px;
}

.filter-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #fff;
  font-weight: 600;
  font-size: 14px;
}

.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn.active {
  background: var(--primary-green);
  border-color: var(--primary-green);
  color: #000;
}

.filter-btn:hover:not(.active) {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

/* Transactions List */
.transactions-list {
  min-height: 200px;
}

.no-transactions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
}

.no-transactions h3 {
  font-size: 18px;
  margin: 16px 0 8px;
  color: #fff;
}

.no-transactions p {
  font-size: 14px;
  margin: 0;
}

.transactions-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.transaction-item:hover {
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.05);
}

.transaction-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  font-size: 16px;
  flex-shrink: 0;
}

.transaction-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.transaction-type {
  font-weight: 600;
  color: #fff;
  font-size: 14px;
}

.transaction-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.transaction-game {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.transaction-amount {
  text-align: right;
  flex-shrink: 0;
}

.amount {
  font-weight: 700;
  font-size: 14px;
}

.amount.positive {
  color: var(--primary-green);
}

.amount.negative {
  color: var(--danger-red);
}

/* Summary */
.history-summary {
  padding: 20px;
}

.history-summary h3 {
  font-size: 16px;
  margin-bottom: 16px;
  color: #fff;
}

.summary-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.stat-value {
  color: #fff;
  font-weight: 600;
  font-size: 14px;
}

/* Responsive */
@media (max-width: 480px) {
  .history-header h1 {
    font-size: 20px;
    gap: 8px;
  }
  
  .history-filters {
    padding: 12px;
  }
  
  .filter-buttons {
    gap: 6px;
  }
  
  .filter-btn {
    padding: 6px 10px;
    font-size: 11px;
  }
  
  .transaction-item {
    gap: 12px;
    padding: 12px;
  }
  
  .transaction-icon {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
  
  .transaction-type {
    font-size: 13px;
  }
  
  .transaction-date {
    font-size: 11px;
  }
  
  .transaction-game {
    font-size: 10px;
  }
  
  .amount {
    font-size: 13px;
  }
  
  .history-summary {
    padding: 16px;
  }
  
  .history-summary h3 {
    font-size: 15px;
  }
  
  .stat-label,
  .stat-value {
    font-size: 13px;
  }
}
