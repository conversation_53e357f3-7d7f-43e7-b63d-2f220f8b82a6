import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/user.entity';
import * as crypto from 'crypto';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async verifyTelegramAuth(initData: string): Promise<boolean> {
    try {
      // Получаем токен бота из переменных окружения
      const botToken = process.env.TELEGRAM_BOT_TOKEN;
      
      if (!botToken) {
        console.warn('TELEGRAM_BOT_TOKEN not set, skipping verification');
        return true; // В тестовом режиме пропускаем проверку
      }

      // Создаем секретный ключ из токена бота
      const secret = crypto.createHash('sha256').update(botToken).digest();
      
      // Парсим initData
      const parsed = new URLSearchParams(initData);
      const hash = parsed.get('hash');
      parsed.delete('hash');

      // Создаем строку для проверки
      const dataCheckString = [...parsed.entries()]
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([k, v]) => `${k}=${v}`)
        .join('\n');

      // Создаем HMAC подпись
      const hmac = crypto.createHmac('sha256', secret).update(dataCheckString).digest('hex');

      // Сравниваем подписи
      return hmac === hash;
    } catch (error) {
      console.error('Error verifying Telegram auth:', error);
      return false;
    }
  }

  async createOrUpdateUser(userData: {
    telegram_id: string;
    username: string;
    firstname: string;
    lastname?: string;
    language_code?: string;
  }): Promise<User> {
    try {
      // Ищем существующего пользователя
      let user = await this.userRepository.findOne({ 
        where: { telegram_id: userData.telegram_id } 
      });

      if (user) {
        // Обновляем данные существующего пользователя
        user.username = userData.username;
        user.firstname = userData.firstname;
        if (userData.lastname) user.lastname = userData.lastname;
        if (userData.language_code) user.language_code = userData.language_code;
        user.updated_at = new Date();
        
        await this.userRepository.save(user);
      } else {
        // Создаем нового пользователя
        user = this.userRepository.create({
          id: userData.telegram_id,
          telegram_id: userData.telegram_id,
          username: userData.username,
          firstname: userData.firstname,
          lastname: userData.lastname || '',
          language_code: userData.language_code || 'ru',
          balance_ton: 0,
        });
        
        await this.userRepository.save(user);
      }

      return user;
    } catch (error) {
      console.error('Error creating/updating user:', error);
      throw error;
    }
  }
}
