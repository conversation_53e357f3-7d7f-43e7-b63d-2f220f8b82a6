#!/bin/bash

echo "🚀 Auto-deploying PEPE CAS to Yandex Cloud"
echo "=========================================="

SERVER_IP="*************"
SERVER_USER="sexuz"

# Create deployment package
echo "📦 Creating deployment package..."
rm -rf deploy-package
mkdir -p deploy-package

# Copy files
cp -r backend deploy-package/
cp -r frontend deploy-package/
cp package.json deploy-package/ 2>/dev/null || true

# Remove node_modules and build folders to reduce size
find deploy-package -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
find deploy-package -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true
find deploy-package -name "build" -type d -exec rm -rf {} + 2>/dev/null || true
find deploy-package -name ".vite" -type d -exec rm -rf {} + 2>/dev/null || true

# Create setup script
cat > deploy-package/setup.sh << 'EOF'
#!/bin/bash
echo "🔧 Setting up PEPE CAS on server..."

# Update system
sudo apt update

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    echo "📦 Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Install PM2
if ! command -v pm2 &> /dev/null; then
    echo "📦 Installing PM2..."
    sudo npm install -g pm2
fi

# Install Nginx
if ! command -v nginx &> /dev/null; then
    echo "📦 Installing Nginx..."
    sudo apt install nginx -y
fi

# Build backend
echo "🔨 Building backend..."
cd backend
npm install
npm run build

# Build frontend
echo "🔨 Building frontend..."
cd ../frontend
npm install
npm run build

# Copy frontend to backend public
echo "📁 Setting up static files..."
mkdir -p ../backend/public
cp -r dist/* ../backend/public/

# Start application
echo "🚀 Starting application..."
cd ../backend
pm2 stop pepe-cas 2>/dev/null || true
pm2 start dist/main.js --name pepe-cas
pm2 save

# Setup PM2 startup
pm2 startup | tail -1 | sudo bash 2>/dev/null || true

# Configure Nginx
echo "🌐 Configuring Nginx..."
sudo tee /etc/nginx/sites-available/pepe-cas > /dev/null << 'NGINX_EOF'
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
NGINX_EOF

# Enable site
sudo ln -sf /etc/nginx/sites-available/pepe-cas /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t && sudo systemctl reload nginx

echo "✅ Deployment completed!"
echo "🌐 App is running at: http://*************"
echo "📱 Configure your Telegram bot Web App URL to: http://*************"
EOF

chmod +x deploy-package/setup.sh

echo "📤 Uploading to server..."
scp -r deploy-package/* ${SERVER_USER}@${SERVER_IP}:~/

echo "🔧 Running setup on server..."
ssh ${SERVER_USER}@${SERVER_IP} 'chmod +x setup.sh && ./setup.sh'

echo "✅ Deployment completed!"
echo "🌐 Your app is running at: http://*************"
echo "📱 Now configuring Telegram bot..."

# Configure Telegram bot
BOT_TOKEN="**********************************************"
WEB_APP_URL="http://*************"

echo "🤖 Setting up Telegram bot Web App..."
curl -X POST "https://api.telegram.org/bot${BOT_TOKEN}/setChatMenuButton" \
-H "Content-Type: application/json" \
-d "{
  \"menu_button\": {
    \"type\": \"web_app\",
    \"text\": \"🎰 Играть\",
    \"web_app\": {
      \"url\": \"${WEB_APP_URL}\"
    }
  }
}"

echo ""
echo "🎉 PEPE CAS Mini App is now live!"
echo "🌐 Web: http://*************"
echo "📱 Telegram: Open your bot and click the menu button"

# Cleanup
rm -rf deploy-package
