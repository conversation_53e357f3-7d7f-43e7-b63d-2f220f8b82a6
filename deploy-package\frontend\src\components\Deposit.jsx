import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Gift,
  Wallet,
  Copy,
  QrC<PERSON>,
  CheckCircle,
  Info,
} from 'lucide-react';
import { useTonConnectUI } from '@tonconnect/ui-react';
import ApiService from '../services/ApiService';
import './Deposit.css';

const Deposit = ({ user, balance, updateBalance }) => {
  const [activeTab, setActiveTab] = useState('gift');
  const [tonConnectUI] = useTonConnectUI();
  const [walletAddress, setWalletAddress] = useState('');
  const [copied, setCopied] = useState(false);
  const [lastGift, setLastGift] = useState(null);

  useEffect(() => {
    // Generate unique wallet address for user
    if (user) {
      const address = generateUserWalletAddress(user.telegram_id);
      setWalletAddress(address);
    }
  }, [user]);

  const generateUserWalletAddress = (telegramId) => {
    // In real implementation, this would be generated on backend
    // For demo purposes, creating a mock address
    const hash = telegramId.toString().padStart(10, '0');
    return `UQA${hash}B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0`;
  };

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const connectWallet = async () => {
    try {
      await tonConnectUI.connectWallet();
    } catch (error) {
      console.error('Failed to connect wallet:', error);
    }
  };

  const tabs = [
    {
      id: 'gift',
      label: 'NFT Подарки',
      icon: Gift,
    },
    {
      id: 'ton',
      label: 'TON Кошелёк',
      icon: Wallet,
    },
  ];

  return (
    <div className="deposit">
      {/* Header */}
      <motion.div
        className="deposit-header"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1>Пополнение баланса</h1>
        <p>Выберите удобный способ пополнения</p>
      </motion.div>

      {/* Tabs */}
      <motion.div
        className="deposit-tabs glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <tab.icon size={20} />
            <span>{tab.label}</span>
          </button>
        ))}
      </motion.div>

      {/* Tab Content */}
      <motion.div
        className="tab-content"
        key={activeTab}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeTab === 'gift' && (
          <div className="gift-deposit glass">
            <div className="deposit-method">
              <div className="method-icon">
                <Gift size={48} className="text-yellow" />
              </div>
              <h2>Пополнение NFT подарками</h2>
              <p className="method-description">
                Отправьте любой NFT подарок боту @pepecas_receiver с комментарием @{user?.username || 'username'}
              </p>
            </div>

            <div className="instruction-steps">
              <div className="step">
                <div className="step-number">1</div>
                <div className="step-content">
                  <h3>Откройте чат с ботом</h3>
                  <p>Найдите @pepecas_receiver в Telegram</p>
                </div>
              </div>
              
              <div className="step">
                <div className="step-number">2</div>
                <div className="step-content">
                  <h3>Отправьте подарок</h3>
                  <p>Переслать любой NFT подарок боту</p>
                </div>
              </div>
              
              <div className="step">
                <div className="step-number">3</div>
                <div className="step-content">
                  <h3>Добавьте комментарий</h3>
                  <div className="username-copy">
                    <code>@{user?.username || 'username'}</code>
                    <button
                      className="copy-button"
                      onClick={() => copyToClipboard(`@${user?.username || 'username'}`)}
                    >
                      {copied ? <CheckCircle size={16} /> : <Copy size={16} />}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="info-box">
              <Info size={20} className="text-yellow" />
              <p>
                Стоимость подарка автоматически зачислится на ваш баланс в течение нескольких минут
              </p>
            </div>

            {lastGift && (
              <div className="last-gift">
                <h3>Последний полученный подарок</h3>
                <div className="gift-item">
                  <img src={lastGift.image_url} alt={lastGift.model_key} />
                  <div className="gift-info">
                    <span className="gift-name">{lastGift.model_key}</span>
                    <span className="gift-value text-green">
                      +{lastGift.min_ton} TON
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'ton' && (
          <div className="ton-deposit glass">
            <div className="deposit-method">
              <div className="method-icon">
                <Wallet size={48} className="text-green" />
              </div>
              <h2>Пополнение TON</h2>
              <p className="method-description">
                Отправьте TON на ваш персональный адрес
              </p>
            </div>

            <div className="wallet-section">
              <h3>Ваш адрес для пополнения:</h3>
              <div className="address-container">
                <div className="address-display">
                  <code className="wallet-address">{walletAddress}</code>
                  <button
                    className="copy-button"
                    onClick={() => copyToClipboard(walletAddress)}
                  >
                    {copied ? <CheckCircle size={16} /> : <Copy size={16} />}
                  </button>
                </div>
              </div>

              <div className="qr-section">
                <div className="qr-placeholder">
                  <QrCode size={72} />
                  <p>QR-код для быстрого пополнения</p>
                </div>
              </div>
            </div>

            <div className="ton-connect-section">
              <h3>Подключить TON кошелёк</h3>
              <p>Подключите ваш TON кошелёк для быстрых переводов</p>
              <button className="btn btn-primary" onClick={connectWallet}>
                <Wallet size={20} />
                Подключить кошелёк
              </button>
            </div>

            <div className="info-box">
              <Info size={20} className="text-green" />
              <p>
                Минимальная сумма пополнения: 0.1 TON. Средства зачисляются после 1 подтверждения
              </p>
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default Deposit;
