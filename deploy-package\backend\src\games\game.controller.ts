import {
  <PERSON>,
  Post,
  Get,
  Body,
  Param,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { GameService } from './game.service';
import { CoinflipService } from './coinflip.service';

@Controller('games')
export class GameController {
  constructor(
    private readonly gameService: GameService,
    private readonly coinflipService: CoinflipService,
  ) {}

  // Crash game endpoints
  @Post('crash/play')
  async playCrash(@Body() body: {
    user_id: string;
    bet_amount: number;
    target_multiplier: number;
  }) {
    if (!body.user_id || body.bet_amount === undefined || body.target_multiplier === undefined) {
      throw new BadRequestException('Missing required fields');
    }

    if (body.bet_amount <= 0 || body.target_multiplier < 1) {
      throw new BadRequestException('Invalid bet amount or multiplier');
    }

    return this.gameService.playCrash(body.user_id, body.bet_amount, body.target_multiplier);
  }

  // Double game endpoints
  @Post('double/play')
  async playDouble(@Body() body: {
    user_id: string;
    bet_amount: number;
    chosen_multiplier: number;
  }) {
    if (!body.user_id || body.bet_amount === undefined || body.chosen_multiplier === undefined) {
      throw new BadRequestException('Missing required fields');
    }

    if (body.bet_amount <= 0) {
      throw new BadRequestException('Invalid bet amount');
    }

    return this.gameService.playDouble(body.user_id, body.bet_amount, body.chosen_multiplier);
  }

  // Coinflip game endpoints
  @Post('coinflip/create')
  async createCoinflipRoom(@Body() body: { user_id: string; stake: number }) {
    if (!body.user_id || body.stake === undefined) {
      throw new BadRequestException('Missing required fields');
    }

    if (body.stake <= 0) {
      throw new BadRequestException('Invalid stake amount');
    }

    const room = await this.coinflipService.createRoom(body.user_id, body.stake);
    if (!room) {
      throw new BadRequestException('Unable to create room - insufficient balance or user banned');
    }

    return room;
  }

  @Post('coinflip/join')
  async joinCoinflipRoom(@Body() body: { room_id: number; user_id: string }) {
    if (!body.room_id || !body.user_id) {
      throw new BadRequestException('Missing required fields');
    }

    const result = await this.coinflipService.joinRoom(body.room_id, body.user_id);
    if (!result.success) {
      throw new BadRequestException('Unable to join room - room not available, insufficient balance, or user banned');
    }

    return result;
  }

  @Get('coinflip/waiting')
  async getWaitingRooms() {
    return this.coinflipService.getWaitingRooms();
  }

  @Get('coinflip/recent')
  async getRecentGames() {
    return this.coinflipService.getRecentGames();
  }

  @Get('coinflip/user/:user_id')
  async getUserRooms(@Param('user_id') user_id: string) {
    return this.coinflipService.getUserRooms(user_id);
  }

  @Get('coinflip/room/:room_id')
  async getRoomById(@Param('room_id') room_id: string) {
    const roomIdNum = parseInt(room_id);
    if (isNaN(roomIdNum)) {
      throw new BadRequestException('Invalid room ID');
    }

    const room = await this.coinflipService.getRoomById(roomIdNum);
    if (!room) {
      throw new NotFoundException('Room not found');
    }

    return room;
  }
}
