/* Profile Component Styles */
.profile {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.profile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 16px;
  color: rgba(255, 255, 255, 0.7);
}

/* Header */
.profile-header {
  text-align: center;
  margin-bottom: 10px;
}

.profile-header h1 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 24px;
  margin-bottom: 8px;
  color: #fff;
}

/* User Info */
.user-info {
  padding: 24px;
  text-align: center;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(0, 255, 136, 0.1));
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-green));
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  color: #000;
}

.user-details h2 {
  font-size: 20px;
  margin-bottom: 4px;
  color: #fff;
}

.username {
  color: var(--primary-green);
  font-weight: 600;
  margin-bottom: 4px;
}

.user-id {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  margin-bottom: 16px;
}

.user-balance {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(0, 255, 136, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(0, 255, 136, 0.3);
  max-width: 200px;
  margin: 0 auto;
}

.balance-amount {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-green);
}

/* Game Statistics */
.game-stats {
  padding: 20px;
}

.game-stats h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  margin-bottom: 20px;
  color: #fff;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-icon {
  font-size: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #fff;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.no-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
}

.no-stats p {
  margin-top: 12px;
  font-size: 14px;
}

/* Win Rate */
.win-rate {
  margin-top: 16px;
}

.win-rate-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.win-rate-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green), var(--primary-gold));
  transition: width 0.5s ease;
}

.win-rate-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  display: block;
}

/* Game Breakdown */
.game-breakdown {
  padding: 20px;
}

.game-breakdown h3 {
  font-size: 16px;
  margin-bottom: 16px;
  color: #fff;
}

.games-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.game-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.game-name {
  font-weight: 600;
  color: #fff;
  font-size: 14px;
}

.game-numbers {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.games-played {
  color: rgba(255, 255, 255, 0.7);
}

.games-won {
  font-weight: 600;
}

/* Wallet Section */
.wallet-section {
  padding: 20px;
}

.wallet-section h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  margin-bottom: 16px;
  color: #fff;
}

.wallet-info {
  text-align: center;
}

.wallet-info p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin-bottom: 16px;
}

.wallet-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  margin: 0 auto;
}

/* Account Info */
.account-info {
  padding: 20px;
}

.account-info h3 {
  font-size: 16px;
  margin-bottom: 16px;
  color: #fff;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.info-value {
  color: #fff;
  font-weight: 600;
  font-size: 14px;
}

/* Responsive */
@media (max-width: 480px) {
  .profile-header h1 {
    font-size: 20px;
    gap: 8px;
  }
  
  .user-info {
    padding: 20px;
  }
  
  .user-avatar {
    width: 70px;
    height: 70px;
    margin-bottom: 12px;
  }
  
  .user-details h2 {
    font-size: 18px;
  }
  
  .user-balance {
    padding: 10px 16px;
  }
  
  .balance-amount {
    font-size: 16px;
  }
  
  .game-stats,
  .game-breakdown,
  .wallet-section,
  .account-info {
    padding: 16px;
  }
  
  .stats-grid {
    gap: 10px;
  }
  
  .stat-card {
    padding: 12px;
    gap: 10px;
  }
  
  .stat-icon {
    font-size: 18px;
    width: 36px;
    height: 36px;
  }
  
  .stat-value {
    font-size: 16px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .game-stat-item {
    padding: 10px;
  }
  
  .game-name {
    font-size: 13px;
  }
  
  .game-numbers {
    gap: 8px;
    font-size: 11px;
  }
  
  .wallet-button {
    padding: 10px 20px;
    font-size: 14px;
  }
  
  .info-label,
  .info-value {
    font-size: 13px;
  }
}
