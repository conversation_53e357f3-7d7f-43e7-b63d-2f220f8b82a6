/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { JsonObject, JsonValue, schema } from '@angular-devkit/core';
export declare function formatValidator(data: JsonValue, dataSchema: JsonObject, formats: schema.SchemaFormat[]): Promise<schema.SchemaValidatorResult>;
