import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from './config/config.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS for frontend
  app.enableCors({
    origin: ['http://localhost:5173', 'https://*.twc1.net', 'https://*.vercel.app'],
    methods: 'GET,POST,PUT,DELETE,PATCH',
    allowedHeaders: 'Content-Type,Authorization',
    credentials: true,
  });

  // Initialize default configs
  const configService = app.get(ConfigService);
  await configService.initializeDefaults();

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
