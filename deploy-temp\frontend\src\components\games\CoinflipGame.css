/* Coinflip Game Styles */
.coinflip-game {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-4);
}

/* Game Header */
.game-header {
  text-align: center;
  padding: var(--space-6);
  position: relative;
  overflow: hidden;
}

.header-icon {
  margin-bottom: var(--space-4);
  display: flex;
  justify-content: center;
}

.stats-row {
  display: flex;
  justify-content: center;
  gap: var(--space-6);
  margin-top: var(--space-4);
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--tertiary-system-background);
  border-radius: 12px;
  border: 1px solid var(--separator);
}

/* Game Tabs */
.game-tabs {
  display: flex;
  padding: var(--space-1);
  gap: var(--space-1);
  background: var(--secondary-system-background);
  border-radius: 16px;
}

.tab-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: transparent;
  border: none;
  border-radius: 12px;
  color: var(--secondary-label);
  font-size: var(--callout);
  font-weight: 600;
  cursor: pointer;
  transition: all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  min-height: 44px;
  position: relative;
}

.tab-btn.active {
  background: var(--system-blue);
  color: #FFFFFF;
  box-shadow: 0 4px 15px rgba(0, 122, 255, 0.4);
}

.tab-btn:hover:not(.active) {
  background: var(--tertiary-system-background);
  color: var(--label);
}

.tab-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: var(--system-red);
  color: #FFFFFF;
  font-size: 10px;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--separator);
}

.section-badge {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  background: var(--tertiary-system-background);
  border-radius: 8px;
  color: var(--secondary-label);
  font-size: var(--footnote);
  font-weight: 600;
}

.section-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--tertiary-system-background);
  border-radius: 12px;
  border: 1px solid var(--separator);
}

/* Rooms List */
.rooms-list {
  padding: var(--space-4);
}

.no-rooms {
  text-align: center;
  padding: var(--space-8) var(--space-4);
}

.empty-icon {
  margin-bottom: var(--space-4);
  display: flex;
  justify-content: center;
  opacity: 0.6;
}

.rooms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-4);
  margin-top: var(--space-4);
}

.room-card {
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  cursor: pointer;
  transition: all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.room-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.room-stake {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.room-status {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.status-dot {
  width: 8px;
  height: 8px;
  background: var(--system-green);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Create Room */
.create-room {
  padding: var(--space-4);
}

.create-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 16px;
  z-index: 2;
  pointer-events: none;
}

.input-with-icon {
  padding-left: 48px;
}

.form-hint {
  margin-top: var(--space-1);
}

/* Button Variants */
.btn-lg {
  padding: 16px 24px;
  font-size: var(--body);
  min-height: 52px;
}

/* Animations */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .coinflip-game {
    padding: var(--space-3);
  }
  
  .stats-row {
    gap: var(--space-3);
  }
  
  .rooms-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
}

@media (max-width: 480px) {
  .tab-btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--subheadline);
  }
  
  .tab-btn span {
    display: none;
  }
  
  .game-header {
    padding: var(--space-4);
  }
  
  .header-icon {
    margin-bottom: var(--space-3);
  }
}
