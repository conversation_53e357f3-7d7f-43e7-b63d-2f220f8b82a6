{"name": "pepe-cas-miniapp", "version": "1.0.0", "description": "PEPE CAS - Telegram Mini App Casino", "scripts": {"install:all": "cd frontend && npm install && cd ../backend && npm install", "build": "cd frontend && npm run build && cd ../backend && npm run build", "start": "cd backend && npm run start:prod", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run start:dev", "deploy:railway": "railway up", "deploy:render": "echo 'Push to GitHub and deploy via Render dashboard'", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["telegram", "mini-app", "casino", "ton", "blockchain"], "author": "PEPE CAS Team", "license": "MIT"}