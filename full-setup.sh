#!/bin/bash

echo "🚀 Full PEPE CAS Setup with HTTPS"
echo "================================="

# Update system
echo "📦 Updating system..."
sudo apt update

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    echo "📦 Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Install PM2
if ! command -v pm2 &> /dev/null; then
    echo "📦 Installing PM2..."
    sudo npm install -g pm2
fi

# Install Nginx and OpenSSL
echo "📦 Installing Nginx and SSL tools..."
sudo apt install nginx openssl -y

# Build backend
echo "🔨 Building backend..."
cd backend
npm install
npm run build

# Build frontend
echo "🔨 Building frontend..."
cd ../frontend
npm install
npm run build

# Copy frontend to backend public
echo "📁 Setting up static files..."
mkdir -p ../backend/public
cp -r dist/* ../backend/public/

# Start application
echo "🚀 Starting application..."
cd ../backend
pm2 stop pepe-cas 2>/dev/null || true
pm2 start dist/main.js --name pepe-cas
pm2 save

# Setup PM2 startup
pm2 startup | tail -1 | sudo bash 2>/dev/null || true

# Generate SSL certificate
echo "🔐 Generating SSL certificate..."
sudo mkdir -p /etc/ssl/private /etc/ssl/certs
sudo openssl req -x509 -newkey rsa:4096 -keyout /etc/ssl/private/pepe-cas.key -out /etc/ssl/certs/pepe-cas.crt -days 365 -nodes -subj "/C=RU/ST=Moscow/L=Moscow/O=PEPE CAS/CN=*************"

# Configure Nginx with HTTPS
echo "🌐 Configuring Nginx with HTTPS..."
sudo tee /etc/nginx/sites-available/pepe-cas > /dev/null << 'NGINX_EOF'
server {
    listen 80;
    server_name _;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name _;

    ssl_certificate /etc/ssl/certs/pepe-cas.crt;
    ssl_certificate_key /etc/ssl/private/pepe-cas.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
NGINX_EOF

# Enable site
sudo ln -sf /etc/nginx/sites-available/pepe-cas /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t && sudo systemctl reload nginx

# Configure Telegram bot with HTTPS
echo "🤖 Configuring Telegram bot..."
curl -X POST "https://api.telegram.org/bot7918026591:AAHrRX3Z1tLtO7aE_gPblArhJMGTv5kixvI/setChatMenuButton" \
-H "Content-Type: application/json" \
-d '{
  "menu_button": {
    "type": "web_app",
    "text": "🎰 Играть",
    "web_app": {
      "url": "https://*************"
    }
  }
}'

echo ""
echo "✅ Deployment completed!"
echo "🌐 App is running at: https://*************"
echo "📱 Open your Telegram bot and click the menu button"
echo ""
echo "🔍 Status check:"
pm2 status
echo ""
sudo systemctl status nginx --no-pager -l
