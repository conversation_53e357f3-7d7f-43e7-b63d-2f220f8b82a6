"use strict";var F=Object.defineProperty;var Gs=Object.getOwnPropertyDescriptor;var Us=Object.getOwnPropertyNames;var Xs=Object.prototype.hasOwnProperty;var Hs=(t,n)=>{for(var e in n)F(t,e,{get:n[e],enumerable:!0})},Js=(t,n,e,s)=>{if(n&&typeof n=="object"||typeof n=="function")for(let u of Us(n))!Xs.call(t,u)&&u!==e&&F(t,u,{get:()=>n[u],enumerable:!(s=Gs(n,u))||s.enumerable});return t};var Zs=t=>Js(F({},"__esModule",{value:!0}),t);var Vu={};Hs(Vu,{BASE64_REGEX:()=>Z,BIC_REGEX:()=>Q,CUID2_REGEX:()=>Y,DECIMAL_REGEX:()=>ee,DIGITS_REGEX:()=>ne,EMAIL_REGEX:()=>te,EMOJI_REGEX:()=>se,HEXADECIMAL_REGEX:()=>ue,HEX_COLOR_REGEX:()=>re,IMEI_REGEX:()=>oe,IPV4_REGEX:()=>ae,IPV6_REGEX:()=>ie,IP_REGEX:()=>Ie,ISO_DATE_REGEX:()=>pe,ISO_DATE_TIME_REGEX:()=>me,ISO_TIMESTAMP_REGEX:()=>de,ISO_TIME_REGEX:()=>ce,ISO_TIME_SECOND_REGEX:()=>Te,ISO_WEEK_REGEX:()=>fe,MAC48_REGEX:()=>ye,MAC64_REGEX:()=>le,MAC_REGEX:()=>he,NANO_ID_REGEX:()=>ke,OCTAL_REGEX:()=>xe,RFC_EMAIL_REGEX:()=>Oe,SLUG_REGEX:()=>ge,ULID_REGEX:()=>we,UUID_REGEX:()=>Se,ValiError:()=>x,_addIssue:()=>r,_getByteCount:()=>A,_getGraphemeCount:()=>b,_getStandardProps:()=>p,_getWordCount:()=>E,_isLuhnAlgo:()=>W,_isValidObjectKey:()=>g,_joinExpects:()=>k,_stringify:()=>f,any:()=>qt,args:()=>ve,argsAsync:()=>qe,array:()=>_t,arrayAsync:()=>Dt,assert:()=>yu,awaitAsync:()=>_e,base64:()=>De,bic:()=>We,bigint:()=>Wt,blob:()=>Vt,boolean:()=>Nt,brand:()=>Ve,bytes:()=>Ne,check:()=>Ce,checkAsync:()=>Le,checkItems:()=>Ke,checkItemsAsync:()=>$e,config:()=>lu,creditCard:()=>Fe,cuid2:()=>ze,custom:()=>Ct,customAsync:()=>Lt,date:()=>Kt,decimal:()=>Ge,deleteGlobalConfig:()=>Ys,deleteGlobalMessage:()=>nu,deleteSchemaMessage:()=>su,deleteSpecificMessage:()=>ru,description:()=>Ue,digits:()=>Xe,email:()=>He,emoji:()=>Je,empty:()=>Ze,endsWith:()=>Qe,entriesFromList:()=>au,entriesFromObjects:()=>iu,enum:()=>$t,enum_:()=>$t,everyItem:()=>Ye,exactOptional:()=>Ft,exactOptionalAsync:()=>zt,excludes:()=>en,fallback:()=>hu,fallbackAsync:()=>ku,file:()=>Gt,filterItems:()=>nn,findItem:()=>tn,finite:()=>sn,flatten:()=>xu,forward:()=>Ou,forwardAsync:()=>gu,function:()=>Ut,function_:()=>Ut,getDefault:()=>y,getDefaults:()=>Be,getDefaultsAsync:()=>Ae,getDotPath:()=>J,getFallback:()=>l,getFallbacks:()=>be,getFallbacksAsync:()=>Ee,getGlobalConfig:()=>w,getGlobalMessage:()=>z,getSchemaMessage:()=>G,getSpecificMessage:()=>U,graphemes:()=>un,gtValue:()=>rn,hash:()=>on,hexColor:()=>In,hexadecimal:()=>an,imei:()=>pn,includes:()=>mn,instance:()=>Xt,integer:()=>cn,intersect:()=>Ht,intersectAsync:()=>Jt,ip:()=>Tn,ipv4:()=>dn,ipv6:()=>fn,is:()=>wu,isOfKind:()=>Iu,isOfType:()=>pu,isValiError:()=>mu,isoDate:()=>yn,isoDateTime:()=>ln,isoTime:()=>hn,isoTimeSecond:()=>kn,isoTimestamp:()=>xn,isoWeek:()=>On,keyof:()=>Su,lazy:()=>Zt,lazyAsync:()=>Qt,length:()=>gn,literal:()=>Yt,looseObject:()=>es,looseObjectAsync:()=>ns,looseTuple:()=>ts,looseTupleAsync:()=>ss,ltValue:()=>wn,mac:()=>Sn,mac48:()=>Bn,mac64:()=>An,map:()=>us,mapAsync:()=>rs,mapItems:()=>bn,maxBytes:()=>En,maxGraphemes:()=>Mn,maxLength:()=>Pn,maxSize:()=>Rn,maxValue:()=>jn,maxWords:()=>vn,metadata:()=>qn,mimeType:()=>_n,minBytes:()=>Dn,minGraphemes:()=>Wn,minLength:()=>Vn,minSize:()=>Nn,minValue:()=>Cn,minWords:()=>Ln,multipleOf:()=>Kn,nan:()=>os,nanoid:()=>$n,never:()=>as,nonEmpty:()=>Fn,nonNullable:()=>is,nonNullableAsync:()=>Is,nonNullish:()=>ps,nonNullishAsync:()=>ms,nonOptional:()=>N,nonOptionalAsync:()=>C,normalize:()=>zn,notBytes:()=>Gn,notGraphemes:()=>Un,notLength:()=>Xn,notSize:()=>Hn,notValue:()=>Jn,notValues:()=>Zn,notWords:()=>Qn,null:()=>cs,null_:()=>cs,nullable:()=>Ts,nullableAsync:()=>ds,nullish:()=>fs,nullishAsync:()=>ys,number:()=>ls,object:()=>hs,objectAsync:()=>ks,objectWithRest:()=>xs,objectWithRestAsync:()=>Os,octal:()=>Yn,omit:()=>Bu,optional:()=>L,optionalAsync:()=>K,parse:()=>Me,parseAsync:()=>Pe,parser:()=>Au,parserAsync:()=>bu,partial:()=>Eu,partialAsync:()=>Mu,partialCheck:()=>et,partialCheckAsync:()=>nt,pick:()=>Pu,picklist:()=>$,pipe:()=>Ru,pipeAsync:()=>ju,promise:()=>gs,rawCheck:()=>tt,rawCheckAsync:()=>st,rawTransform:()=>ut,rawTransformAsync:()=>rt,readonly:()=>ot,record:()=>ws,recordAsync:()=>Ss,reduceItems:()=>at,regex:()=>it,required:()=>vu,requiredAsync:()=>qu,returns:()=>It,returnsAsync:()=>pt,rfcEmail:()=>mt,safeInteger:()=>ct,safeParse:()=>Re,safeParseAsync:()=>je,safeParser:()=>_u,safeParserAsync:()=>Du,set:()=>Bs,setAsync:()=>As,setGlobalConfig:()=>Qs,setGlobalMessage:()=>eu,setSchemaMessage:()=>tu,setSpecificMessage:()=>uu,size:()=>Tt,slug:()=>dt,someItem:()=>ft,sortItems:()=>yt,startsWith:()=>lt,strictObject:()=>bs,strictObjectAsync:()=>Es,strictTuple:()=>Ms,strictTupleAsync:()=>Ps,string:()=>Rs,symbol:()=>js,title:()=>ht,toLowerCase:()=>kt,toMaxValue:()=>xt,toMinValue:()=>Ot,toUpperCase:()=>gt,transform:()=>wt,transformAsync:()=>St,trim:()=>Bt,trimEnd:()=>At,trimStart:()=>bt,tuple:()=>vs,tupleAsync:()=>qs,tupleWithRest:()=>_s,tupleWithRestAsync:()=>Ds,ulid:()=>Et,undefined:()=>Ws,undefined_:()=>Ws,undefinedable:()=>Vs,undefinedableAsync:()=>Ns,union:()=>Cs,unionAsync:()=>Ls,unknown:()=>Ks,unwrap:()=>Wu,url:()=>Mt,uuid:()=>Pt,value:()=>Rt,values:()=>jt,variant:()=>$s,variantAsync:()=>Fs,void:()=>zs,void_:()=>zs,words:()=>vt});module.exports=Zs(Vu);var P;function Qs(t){P={...P,...t}}function w(t){return{lang:t?.lang??P?.lang,message:t?.message,abortEarly:t?.abortEarly??P?.abortEarly,abortPipeEarly:t?.abortPipeEarly??P?.abortPipeEarly}}function Ys(){P=void 0}var q;function eu(t,n){q||(q=new Map),q.set(n,t)}function z(t){return q?.get(t)}function nu(t){q?.delete(t)}var _;function tu(t,n){_||(_=new Map),_.set(n,t)}function G(t){return _?.get(t)}function su(t){_?.delete(t)}var M;function uu(t,n,e){M||(M=new Map),M.get(t)||M.set(t,new Map),M.get(t).set(e,n)}function U(t,n){return M?.get(t)?.get(n)}function ru(t,n){M?.get(t)?.delete(n)}function f(t){let n=typeof t;return n==="string"?`"${t}"`:n==="number"||n==="bigint"||n==="boolean"?`${t}`:n==="object"||n==="function"?(t&&Object.getPrototypeOf(t)?.constructor?.name)??"null":n}function r(t,n,e,s,u){let o=u&&"input"in u?u.input:e.value,a=u?.expected??t.expects??null,I=u?.received??f(o),i={kind:t.kind,type:t.type,input:o,expected:a,received:I,message:`Invalid ${n}: ${a?`Expected ${a} but r`:"R"}eceived ${I}`,requirement:t.requirement,path:u?.path,issues:u?.issues,lang:s.lang,abortEarly:s.abortEarly,abortPipeEarly:s.abortPipeEarly},m=t.kind==="schema",c=u?.message??t.message??U(t.reference,i.lang)??(m?G(i.lang):null)??s.message??z(i.lang);c!==void 0&&(i.message=typeof c=="function"?c(i):c),m&&(e.typed=!1),e.issues?e.issues.push(i):e.issues=[i]}var X;function A(t){return X||(X=new TextEncoder),X.encode(t).length}var H;function b(t){H||(H=new Intl.Segmenter);let n=H.segment(t),e=0;for(let s of n)e++;return e}function p(t){return{version:1,vendor:"valibot",validate(n){return t["~run"]({value:n},w())}}}var D;function E(t,n){D||(D=new Map),D.get(t)||D.set(t,new Intl.Segmenter(t,{granularity:"word"}));let e=D.get(t).segment(n),s=0;for(let u of e)u.isWordLike&&s++;return s}var ou=/\D/gu;function W(t){let n=t.replace(ou,""),e=n.length,s=1,u=0;for(;e;){let o=+n[--e];s^=1,u+=s?[0,2,4,6,8,1,3,5,7,9][o]:o}return u%10===0}function g(t,n){return Object.hasOwn(t,n)&&n!=="__proto__"&&n!=="prototype"&&n!=="constructor"}function k(t,n){let e=[...new Set(t)];return e.length>1?`(${e.join(` ${n} `)})`:e[0]??"never"}function au(t,n){let e={};for(let s of t)e[s]=n;return e}function iu(t){let n={};for(let e of t)Object.assign(n,e.entries);return n}function J(t){if(t.path){let n="";for(let e of t.path)if(typeof e.key=="string"||typeof e.key=="number")n?n+=`.${e.key}`:n+=e.key;else return null;return n}return null}function Iu(t,n){return n.kind===t}function pu(t,n){return n.type===t}function mu(t){return t instanceof x}var x=class extends Error{constructor(n){super(n[0].message),this.name="ValiError",this.issues=n}};function ve(t){return{kind:"transformation",type:"args",reference:ve,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=(...u)=>{let o=this.schema["~run"]({value:u},e);if(o.issues)throw new x(o.issues);return s(...o.value)},n}}}function qe(t){return{kind:"transformation",type:"args",reference:qe,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=async(...u)=>{let o=await t["~run"]({value:u},e);if(o.issues)throw new x(o.issues);return s(...o.value)},n}}}function _e(){return{kind:"transformation",type:"await",reference:_e,async:!0,async"~run"(t){return t.value=await t.value,t}}}var Z=/^(?:[\da-z+/]{4})*(?:[\da-z+/]{2}==|[\da-z+/]{3}=)?$/iu,Q=/^[A-Z]{6}(?!00)[\dA-Z]{2}(?:[\dA-Z]{3})?$/u,Y=/^[a-z][\da-z]*$/u,ee=/^[+-]?(?:\d*\.)?\d+$/u,ne=/^\d+$/u,te=/^[\w+-]+(?:\.[\w+-]+)*@[\da-z]+(?:[.-][\da-z]+)*\.[a-z]{2,}$/iu,se=/^(?:[\u{1F1E6}-\u{1F1FF}]{2}|\u{1F3F4}[\u{E0061}-\u{E007A}]{2}[\u{E0030}-\u{E0039}\u{E0061}-\u{E007A}]{1,3}\u{E007F}|(?:\p{Emoji}\uFE0F\u20E3?|\p{Emoji_Modifier_Base}\p{Emoji_Modifier}?|\p{Emoji_Presentation})(?:\u200D(?:\p{Emoji}\uFE0F\u20E3?|\p{Emoji_Modifier_Base}\p{Emoji_Modifier}?|\p{Emoji_Presentation}))*)+$/u,ue=/^(?:0[hx])?[\da-fA-F]+$/u,re=/^#(?:[\da-fA-F]{3,4}|[\da-fA-F]{6}|[\da-fA-F]{8})$/u,oe=/^\d{15}$|^\d{2}-\d{6}-\d{6}-\d$/u,ae=/^(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])(?:\.(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])){3}$/u,ie=/^(?:(?:[\da-f]{1,4}:){7}[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,7}:|(?:[\da-f]{1,4}:){1,6}:[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,5}(?::[\da-f]{1,4}){1,2}|(?:[\da-f]{1,4}:){1,4}(?::[\da-f]{1,4}){1,3}|(?:[\da-f]{1,4}:){1,3}(?::[\da-f]{1,4}){1,4}|(?:[\da-f]{1,4}:){1,2}(?::[\da-f]{1,4}){1,5}|[\da-f]{1,4}:(?::[\da-f]{1,4}){1,6}|:(?:(?::[\da-f]{1,4}){1,7}|:)|fe80:(?::[\da-f]{0,4}){0,4}%[\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d)|(?:[\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d))$/iu,Ie=/^(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])(?:\.(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])){3}$|^(?:(?:[\da-f]{1,4}:){7}[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,7}:|(?:[\da-f]{1,4}:){1,6}:[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,5}(?::[\da-f]{1,4}){1,2}|(?:[\da-f]{1,4}:){1,4}(?::[\da-f]{1,4}){1,3}|(?:[\da-f]{1,4}:){1,3}(?::[\da-f]{1,4}){1,4}|(?:[\da-f]{1,4}:){1,2}(?::[\da-f]{1,4}){1,5}|[\da-f]{1,4}:(?::[\da-f]{1,4}){1,6}|:(?:(?::[\da-f]{1,4}){1,7}|:)|fe80:(?::[\da-f]{0,4}){0,4}%[\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d)|(?:[\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d))$/iu,pe=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])$/u,me=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])[T ](?:0\d|1\d|2[0-3]):[0-5]\d$/u,ce=/^(?:0\d|1\d|2[0-3]):[0-5]\d$/u,Te=/^(?:0\d|1\d|2[0-3])(?::[0-5]\d){2}$/u,de=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])[T ](?:0\d|1\d|2[0-3])(?::[0-5]\d){2}(?:\.\d{1,9})?(?:Z|[+-](?:0\d|1\d|2[0-3])(?::?[0-5]\d)?)$/u,fe=/^\d{4}-W(?:0[1-9]|[1-4]\d|5[0-3])$/u,ye=/^(?:[\da-f]{2}:){5}[\da-f]{2}$|^(?:[\da-f]{2}-){5}[\da-f]{2}$|^(?:[\da-f]{4}\.){2}[\da-f]{4}$/iu,le=/^(?:[\da-f]{2}:){7}[\da-f]{2}$|^(?:[\da-f]{2}-){7}[\da-f]{2}$|^(?:[\da-f]{4}\.){3}[\da-f]{4}$|^(?:[\da-f]{4}:){3}[\da-f]{4}$/iu,he=/^(?:[\da-f]{2}:){5}[\da-f]{2}$|^(?:[\da-f]{2}-){5}[\da-f]{2}$|^(?:[\da-f]{4}\.){2}[\da-f]{4}$|^(?:[\da-f]{2}:){7}[\da-f]{2}$|^(?:[\da-f]{2}-){7}[\da-f]{2}$|^(?:[\da-f]{4}\.){3}[\da-f]{4}$|^(?:[\da-f]{4}:){3}[\da-f]{4}$/iu,ke=/^[\w-]+$/u,xe=/^(?:0o)?[0-7]+$/u,Oe=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,ge=/^[\da-z]+(?:[-_][\da-z]+)*$/u,we=/^[\da-hjkmnp-tv-zA-HJKMNP-TV-Z]{26}$/u,Se=/^[\da-f]{8}(?:-[\da-f]{4}){3}-[\da-f]{12}$/iu;function De(t){return{kind:"validation",type:"base64",reference:De,async:!1,expects:null,requirement:Z,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Base64",n,e),n}}}function We(t){return{kind:"validation",type:"bic",reference:We,async:!1,expects:null,requirement:Q,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"BIC",n,e),n}}}function Ve(t){return{kind:"transformation",type:"brand",reference:Ve,async:!1,name:t,"~run"(n){return n}}}function Ne(t,n){return{kind:"validation",type:"bytes",reference:Ne,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u!==this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function Ce(t,n){return{kind:"validation",type:"check",reference:Ce,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement(e.value)&&r(this,"input",e,s),e}}}function Le(t,n){return{kind:"validation",type:"check",reference:Le,async:!0,expects:null,requirement:t,message:n,async"~run"(e,s){return e.typed&&!await this.requirement(e.value)&&r(this,"input",e,s),e}}}function Ke(t,n){return{kind:"validation",type:"check_items",reference:Ke,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){if(e.typed)for(let u=0;u<e.value.length;u++){let o=e.value[u];this.requirement(o,u,e.value)||r(this,"item",e,s,{input:o,path:[{type:"array",origin:"value",input:e.value,key:u,value:o}]})}return e}}}function $e(t,n){return{kind:"validation",type:"check_items",reference:$e,async:!0,expects:null,requirement:t,message:n,async"~run"(e,s){if(e.typed){let u=await Promise.all(e.value.map(this.requirement));for(let o=0;o<e.value.length;o++)if(!u[o]){let a=e.value[o];r(this,"item",e,s,{input:a,path:[{type:"array",origin:"value",input:e.value,key:o,value:a}]})}}return e}}}var cu=/^(?:\d{14,19}|\d{4}(?: \d{3,6}){2,4}|\d{4}(?:-\d{3,6}){2,4})$/u,Tu=/[- ]/gu,du=[/^3[47]\d{13}$/u,/^3(?:0[0-5]|[68]\d)\d{11,13}$/u,/^6(?:011|5\d{2})\d{12,15}$/u,/^(?:2131|1800|35\d{3})\d{11}$/u,/^5[1-5]\d{2}|(?:222\d|22[3-9]\d|2[3-6]\d{2}|27[01]\d|2720)\d{12}$/u,/^(?:6[27]\d{14,17}|81\d{14,17})$/u,/^4\d{12}(?:\d{3,6})?$/u];function Fe(t){return{kind:"validation",type:"credit_card",reference:Fe,async:!1,expects:null,requirement(n){let e;return cu.test(n)&&(e=n.replace(Tu,""))&&du.some(s=>s.test(e))&&W(e)},message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"credit card",n,e),n}}}function ze(t){return{kind:"validation",type:"cuid2",reference:ze,async:!1,expects:null,requirement:Y,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Cuid2",n,e),n}}}function Ge(t){return{kind:"validation",type:"decimal",reference:Ge,async:!1,expects:null,requirement:ee,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"decimal",n,e),n}}}function Ue(t){return{kind:"metadata",type:"description",reference:Ue,description:t}}function Xe(t){return{kind:"validation",type:"digits",reference:Xe,async:!1,expects:null,requirement:ne,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"digits",n,e),n}}}function He(t){return{kind:"validation",type:"email",reference:He,expects:null,async:!1,requirement:te,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"email",n,e),n}}}function Je(t){return{kind:"validation",type:"emoji",reference:Je,async:!1,expects:null,requirement:se,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"emoji",n,e),n}}}function Ze(t){return{kind:"validation",type:"empty",reference:Ze,async:!1,expects:"0",message:t,"~run"(n,e){return n.typed&&n.value.length>0&&r(this,"length",n,e,{received:`${n.value.length}`}),n}}}function Qe(t,n){return{kind:"validation",type:"ends_with",reference:Qe,async:!1,expects:`"${t}"`,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.endsWith(this.requirement)&&r(this,"end",e,s,{received:`"${e.value.slice(-this.requirement.length)}"`}),e}}}function Ye(t,n){return{kind:"validation",type:"every_item",reference:Ye,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.every(this.requirement)&&r(this,"item",e,s),e}}}function en(t,n){let e=f(t);return{kind:"validation",type:"excludes",reference:en,async:!1,expects:`!${e}`,requirement:t,message:n,"~run"(s,u){return s.typed&&s.value.includes(this.requirement)&&r(this,"content",s,u,{received:e}),s}}}function nn(t){return{kind:"transformation",type:"filter_items",reference:nn,async:!1,operation:t,"~run"(n){return n.value=n.value.filter(this.operation),n}}}function tn(t){return{kind:"transformation",type:"find_item",reference:tn,async:!1,operation:t,"~run"(n){return n.value=n.value.find(this.operation),n}}}function sn(t){return{kind:"validation",type:"finite",reference:sn,async:!1,expects:null,requirement:Number.isFinite,message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"finite",n,e),n}}}function un(t,n){return{kind:"validation",type:"graphemes",reference:un,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=b(e.value);u!==this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function rn(t,n){return{kind:"validation",type:"gt_value",reference:rn,async:!1,expects:`>${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value>this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}var fu={md4:32,md5:32,sha1:40,sha256:64,sha384:96,sha512:128,ripemd128:32,ripemd160:40,tiger128:32,tiger160:40,tiger192:48,crc32:8,crc32b:8,adler32:8};function on(t,n){return{kind:"validation",type:"hash",reference:on,expects:null,async:!1,requirement:RegExp(t.map(e=>`^[a-f0-9]{${fu[e]}}$`).join("|"),"iu"),message:n,"~run"(e,s){return e.typed&&!this.requirement.test(e.value)&&r(this,"hash",e,s),e}}}function an(t){return{kind:"validation",type:"hexadecimal",reference:an,async:!1,expects:null,requirement:ue,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"hexadecimal",n,e),n}}}function In(t){return{kind:"validation",type:"hex_color",reference:In,async:!1,expects:null,requirement:re,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"hex color",n,e),n}}}function pn(t){return{kind:"validation",type:"imei",reference:pn,async:!1,expects:null,requirement(n){return oe.test(n)&&W(n)},message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"IMEI",n,e),n}}}function mn(t,n){let e=f(t);return{kind:"validation",type:"includes",reference:mn,async:!1,expects:e,requirement:t,message:n,"~run"(s,u){return s.typed&&!s.value.includes(this.requirement)&&r(this,"content",s,u,{received:`!${e}`}),s}}}function cn(t){return{kind:"validation",type:"integer",reference:cn,async:!1,expects:null,requirement:Number.isInteger,message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"integer",n,e),n}}}function Tn(t){return{kind:"validation",type:"ip",reference:Tn,async:!1,expects:null,requirement:Ie,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IP",n,e),n}}}function dn(t){return{kind:"validation",type:"ipv4",reference:dn,async:!1,expects:null,requirement:ae,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IPv4",n,e),n}}}function fn(t){return{kind:"validation",type:"ipv6",reference:fn,async:!1,expects:null,requirement:ie,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IPv6",n,e),n}}}function yn(t){return{kind:"validation",type:"iso_date",reference:yn,async:!1,expects:null,requirement:pe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"date",n,e),n}}}function ln(t){return{kind:"validation",type:"iso_date_time",reference:ln,async:!1,expects:null,requirement:me,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"date-time",n,e),n}}}function hn(t){return{kind:"validation",type:"iso_time",reference:hn,async:!1,expects:null,requirement:ce,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"time",n,e),n}}}function kn(t){return{kind:"validation",type:"iso_time_second",reference:kn,async:!1,expects:null,requirement:Te,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"time-second",n,e),n}}}function xn(t){return{kind:"validation",type:"iso_timestamp",reference:xn,async:!1,expects:null,requirement:de,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"timestamp",n,e),n}}}function On(t){return{kind:"validation",type:"iso_week",reference:On,async:!1,expects:null,requirement:fe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"week",n,e),n}}}function gn(t,n){return{kind:"validation",type:"length",reference:gn,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length!==this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function wn(t,n){return{kind:"validation",type:"lt_value",reference:wn,async:!1,expects:`<${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value<this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Sn(t){return{kind:"validation",type:"mac",reference:Sn,async:!1,expects:null,requirement:he,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"MAC",n,e),n}}}function Bn(t){return{kind:"validation",type:"mac48",reference:Bn,async:!1,expects:null,requirement:ye,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"48-bit MAC",n,e),n}}}function An(t){return{kind:"validation",type:"mac64",reference:An,async:!1,expects:null,requirement:le,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"64-bit MAC",n,e),n}}}function bn(t){return{kind:"transformation",type:"map_items",reference:bn,async:!1,operation:t,"~run"(n){return n.value=n.value.map(this.operation),n}}}function En(t,n){return{kind:"validation",type:"max_bytes",reference:En,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u>this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function Mn(t,n){return{kind:"validation",type:"max_graphemes",reference:Mn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=b(e.value);u>this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function Pn(t,n){return{kind:"validation",type:"max_length",reference:Pn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length>this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function Rn(t,n){return{kind:"validation",type:"max_size",reference:Rn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size>this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function jn(t,n){return{kind:"validation",type:"max_value",reference:jn,async:!1,expects:`<=${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value<=this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function vn(t,n,e){return{kind:"validation",type:"max_words",reference:vn,async:!1,expects:`<=${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=E(this.locales,s.value);o>this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function qn(t){return{kind:"metadata",type:"metadata",reference:qn,metadata:t}}function _n(t,n){return{kind:"validation",type:"mime_type",reference:_n,async:!1,expects:k(t.map(e=>`"${e}"`),"|"),requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement.includes(e.value.type)&&r(this,"MIME type",e,s,{received:`"${e.value.type}"`}),e}}}function Dn(t,n){return{kind:"validation",type:"min_bytes",reference:Dn,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u<this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function Wn(t,n){return{kind:"validation",type:"min_graphemes",reference:Wn,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=b(e.value);u<this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function Vn(t,n){return{kind:"validation",type:"min_length",reference:Vn,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length<this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function Nn(t,n){return{kind:"validation",type:"min_size",reference:Nn,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size<this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function Cn(t,n){return{kind:"validation",type:"min_value",reference:Cn,async:!1,expects:`>=${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value>=this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Ln(t,n,e){return{kind:"validation",type:"min_words",reference:Ln,async:!1,expects:`>=${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=E(this.locales,s.value);o<this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function Kn(t,n){return{kind:"validation",type:"multiple_of",reference:Kn,async:!1,expects:`%${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value%this.requirement!==0&&r(this,"multiple",e,s),e}}}function $n(t){return{kind:"validation",type:"nanoid",reference:$n,async:!1,expects:null,requirement:ke,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Nano ID",n,e),n}}}function Fn(t){return{kind:"validation",type:"non_empty",reference:Fn,async:!1,expects:"!0",message:t,"~run"(n,e){return n.typed&&n.value.length===0&&r(this,"length",n,e,{received:"0"}),n}}}function zn(t){return{kind:"transformation",type:"normalize",reference:zn,async:!1,form:t,"~run"(n){return n.value=n.value.normalize(this.form),n}}}function Gn(t,n){return{kind:"validation",type:"not_bytes",reference:Gn,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u===this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function Un(t,n){return{kind:"validation",type:"not_graphemes",reference:Un,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=b(e.value);u===this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function Xn(t,n){return{kind:"validation",type:"not_length",reference:Xn,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length===this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function Hn(t,n){return{kind:"validation",type:"not_size",reference:Hn,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size===this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function Jn(t,n){return{kind:"validation",type:"not_value",reference:Jn,async:!1,expects:t instanceof Date?`!${t.toJSON()}`:`!${f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&this.requirement<=e.value&&this.requirement>=e.value&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Zn(t,n){return{kind:"validation",type:"not_values",reference:Zn,async:!1,expects:`!${k(t.map(e=>e instanceof Date?e.toJSON():f(e)),"|")}`,requirement:t,message:n,"~run"(e,s){return e.typed&&this.requirement.some(u=>u<=e.value&&u>=e.value)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Qn(t,n,e){return{kind:"validation",type:"not_words",reference:Qn,async:!1,expects:`!${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=E(this.locales,s.value);o===this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function Yn(t){return{kind:"validation",type:"octal",reference:Yn,async:!1,expects:null,requirement:xe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"octal",n,e),n}}}function V(t,n){if(t.issues)for(let e of n)for(let s of t.issues){let u=!1,o=Math.min(e.length,s.path?.length??0);for(let a=0;a<o;a++)if(e[a]!==s.path[a].key&&(e[a]!=="$"||s.path[a].type!=="array")){u=!0;break}if(!u)return!1}return!0}function et(t,n,e){return{kind:"validation",type:"partial_check",reference:et,async:!1,expects:null,paths:t,requirement:n,message:e,"~run"(s,u){return(s.typed||V(s,t))&&!this.requirement(s.value)&&r(this,"input",s,u),s}}}function nt(t,n,e){return{kind:"validation",type:"partial_check",reference:nt,async:!0,expects:null,paths:t,requirement:n,message:e,async"~run"(s,u){return(s.typed||V(s,t))&&!await this.requirement(s.value)&&r(this,"input",s,u),s}}}function tt(t){return{kind:"validation",type:"raw_check",reference:tt,async:!1,expects:null,"~run"(n,e){return t({dataset:n,config:e,addIssue:s=>r(this,s?.label??"input",n,e,s)}),n}}}function st(t){return{kind:"validation",type:"raw_check",reference:st,async:!0,expects:null,async"~run"(n,e){return await t({dataset:n,config:e,addIssue:s=>r(this,s?.label??"input",n,e,s)}),n}}}function ut(t){return{kind:"transformation",type:"raw_transform",reference:ut,async:!1,"~run"(n,e){let s=t({dataset:n,config:e,addIssue:u=>r(this,u?.label??"input",n,e,u),NEVER:null});return n.issues?n.typed=!1:n.value=s,n}}}function rt(t){return{kind:"transformation",type:"raw_transform",reference:rt,async:!0,async"~run"(n,e){let s=await t({dataset:n,config:e,addIssue:u=>r(this,u?.label??"input",n,e,u),NEVER:null});return n.issues?n.typed=!1:n.value=s,n}}}function ot(){return{kind:"transformation",type:"readonly",reference:ot,async:!1,"~run"(t){return t}}}function at(t,n){return{kind:"transformation",type:"reduce_items",reference:at,async:!1,operation:t,initial:n,"~run"(e){return e.value=e.value.reduce(this.operation,this.initial),e}}}function it(t,n){return{kind:"validation",type:"regex",reference:it,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement.test(e.value)&&r(this,"format",e,s),e}}}function It(t){return{kind:"transformation",type:"returns",reference:It,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=(...u)=>{let o=this.schema["~run"]({value:s(...u)},e);if(o.issues)throw new x(o.issues);return o.value},n}}}function pt(t){return{kind:"transformation",type:"returns",reference:pt,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=async(...u)=>{let o=await this.schema["~run"]({value:await s(...u)},e);if(o.issues)throw new x(o.issues);return o.value},n}}}function mt(t){return{kind:"validation",type:"rfc_email",reference:mt,expects:null,async:!1,requirement:Oe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"email",n,e),n}}}function ct(t){return{kind:"validation",type:"safe_integer",reference:ct,async:!1,expects:null,requirement:Number.isSafeInteger,message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"safe integer",n,e),n}}}function Tt(t,n){return{kind:"validation",type:"size",reference:Tt,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size!==this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function dt(t){return{kind:"validation",type:"slug",reference:dt,async:!1,expects:null,requirement:ge,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"slug",n,e),n}}}function ft(t,n){return{kind:"validation",type:"some_item",reference:ft,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.some(this.requirement)&&r(this,"item",e,s),e}}}function yt(t){return{kind:"transformation",type:"sort_items",reference:yt,async:!1,operation:t,"~run"(n){return n.value=n.value.sort(this.operation),n}}}function lt(t,n){return{kind:"validation",type:"starts_with",reference:lt,async:!1,expects:`"${t}"`,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.startsWith(this.requirement)&&r(this,"start",e,s,{received:`"${e.value.slice(0,this.requirement.length)}"`}),e}}}function ht(t){return{kind:"metadata",type:"title",reference:ht,title:t}}function kt(){return{kind:"transformation",type:"to_lower_case",reference:kt,async:!1,"~run"(t){return t.value=t.value.toLowerCase(),t}}}function xt(t){return{kind:"transformation",type:"to_max_value",reference:xt,async:!1,requirement:t,"~run"(n){return n.value=n.value>this.requirement?this.requirement:n.value,n}}}function Ot(t){return{kind:"transformation",type:"to_min_value",reference:Ot,async:!1,requirement:t,"~run"(n){return n.value=n.value<this.requirement?this.requirement:n.value,n}}}function gt(){return{kind:"transformation",type:"to_upper_case",reference:gt,async:!1,"~run"(t){return t.value=t.value.toUpperCase(),t}}}function wt(t){return{kind:"transformation",type:"transform",reference:wt,async:!1,operation:t,"~run"(n){return n.value=this.operation(n.value),n}}}function St(t){return{kind:"transformation",type:"transform",reference:St,async:!0,operation:t,async"~run"(n){return n.value=await this.operation(n.value),n}}}function Bt(){return{kind:"transformation",type:"trim",reference:Bt,async:!1,"~run"(t){return t.value=t.value.trim(),t}}}function At(){return{kind:"transformation",type:"trim_end",reference:At,async:!1,"~run"(t){return t.value=t.value.trimEnd(),t}}}function bt(){return{kind:"transformation",type:"trim_start",reference:bt,async:!1,"~run"(t){return t.value=t.value.trimStart(),t}}}function Et(t){return{kind:"validation",type:"ulid",reference:Et,async:!1,expects:null,requirement:we,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"ULID",n,e),n}}}function Mt(t){return{kind:"validation",type:"url",reference:Mt,async:!1,expects:null,requirement(n){try{return new URL(n),!0}catch{return!1}},message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"URL",n,e),n}}}function Pt(t){return{kind:"validation",type:"uuid",reference:Pt,async:!1,expects:null,requirement:Se,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"UUID",n,e),n}}}function Rt(t,n){return{kind:"validation",type:"value",reference:Rt,async:!1,expects:t instanceof Date?t.toJSON():f(t),requirement:t,message:n,"~run"(e,s){return e.typed&&!(this.requirement<=e.value&&this.requirement>=e.value)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function jt(t,n){return{kind:"validation",type:"values",reference:jt,async:!1,expects:`${k(t.map(e=>e instanceof Date?e.toJSON():f(e)),"|")}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement.some(u=>u<=e.value&&u>=e.value)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function vt(t,n,e){return{kind:"validation",type:"words",reference:vt,async:!1,expects:`${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=E(this.locales,s.value);o!==this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function yu(t,n){let e=t["~run"]({value:n},{abortEarly:!0}).issues;if(e)throw new x(e)}function lu(t,n){return{...t,get"~standard"(){return p(this)},"~run"(e,s){return t["~run"](e,{...s,...n})}}}function l(t,n,e){return typeof t.fallback=="function"?t.fallback(n,e):t.fallback}function hu(t,n){return{...t,fallback:n,get"~standard"(){return p(this)},"~run"(e,s){let u=t["~run"](e,s);return u.issues?{typed:!0,value:l(this,u,s)}:u}}}function ku(t,n){return{...t,fallback:n,async:!0,get"~standard"(){return p(this)},async"~run"(e,s){let u=await t["~run"](e,s);return u.issues?{typed:!0,value:await l(this,u,s)}:u}}}function xu(t){let n={};for(let e of t)if(e.path){let s=J(e);s?(n.nested||(n.nested={}),n.nested[s]?n.nested[s].push(e.message):n.nested[s]=[e.message]):n.other?n.other.push(e.message):n.other=[e.message]}else n.root?n.root.push(e.message):n.root=[e.message];return n}function Ou(t,n){return{...t,"~run"(e,s){let u=e.issues&&[...e.issues];if(e=t["~run"](e,s),e.issues){for(let o of e.issues)if(!u?.includes(o)){let a=e.value;for(let I of n){let i=a[I],m={type:"unknown",origin:"value",input:a,key:I,value:i};if(o.path?o.path.push(m):o.path=[m],!i)break;a=i}}}return e}}}function gu(t,n){return{...t,async:!0,async"~run"(e,s){let u=e.issues&&[...e.issues];if(e=await t["~run"](e,s),e.issues){for(let o of e.issues)if(!u?.includes(o)){let a=e.value;for(let I of n){let i=a[I],m={type:"unknown",origin:"value",input:a,key:I,value:i};if(o.path?o.path.push(m):o.path=[m],!i)break;a=i}}}return e}}}function y(t,n,e){return typeof t.default=="function"?t.default(n,e):t.default}function Be(t){if("entries"in t){let n={};for(let e in t.entries)n[e]=Be(t.entries[e]);return n}return"items"in t?t.items.map(Be):y(t)}async function Ae(t){return"entries"in t?Object.fromEntries(await Promise.all(Object.entries(t.entries).map(async([n,e])=>[n,await Ae(e)]))):"items"in t?Promise.all(t.items.map(Ae)):y(t)}function be(t){if("entries"in t){let n={};for(let e in t.entries)n[e]=be(t.entries[e]);return n}return"items"in t?t.items.map(be):l(t)}async function Ee(t){return"entries"in t?Object.fromEntries(await Promise.all(Object.entries(t.entries).map(async([n,e])=>[n,await Ee(e)]))):"items"in t?Promise.all(t.items.map(Ee)):l(t)}function wu(t,n){return!t["~run"]({value:n},{abortEarly:!0}).issues}function qt(){return{kind:"schema",type:"any",reference:qt,expects:"any",async:!1,get"~standard"(){return p(this)},"~run"(t){return t.typed=!0,t}}}function _t(t,n){return{kind:"schema",type:"array",reference:_t,expects:"Array",async:!1,item:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<u.length;o++){let a=u[o],I=this.item["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}}else r(this,"type",e,s);return e}}}function Dt(t,n){return{kind:"schema",type:"array",reference:Dt,expects:"Array",async:!0,item:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(u.map(a=>this.item["~run"]({value:a},s)));for(let a=0;a<o.length;a++){let I=o[a];if(I.issues){let i={type:"array",origin:"value",input:u,key:a,value:u[a]};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}}else r(this,"type",e,s);return e}}}function Wt(t){return{kind:"schema",type:"bigint",reference:Wt,expects:"bigint",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="bigint"?n.typed=!0:r(this,"type",n,e),n}}}function Vt(t){return{kind:"schema",type:"blob",reference:Vt,expects:"Blob",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof Blob?n.typed=!0:r(this,"type",n,e),n}}}function Nt(t){return{kind:"schema",type:"boolean",reference:Nt,expects:"boolean",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="boolean"?n.typed=!0:r(this,"type",n,e),n}}}function Ct(t,n){return{kind:"schema",type:"custom",reference:Ct,expects:"unknown",async:!1,check:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return this.check(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function Lt(t,n){return{kind:"schema",type:"custom",reference:Lt,expects:"unknown",async:!0,check:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return await this.check(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function Kt(t){return{kind:"schema",type:"date",reference:Kt,expects:"Date",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof Date?isNaN(n.value)?r(this,"type",n,e,{received:'"Invalid Date"'}):n.typed=!0:r(this,"type",n,e),n}}}function $t(t,n){let e=[];for(let s in t)(`${+s}`!==s||typeof t[s]!="string"||!Object.is(t[t[s]],+s))&&e.push(t[s]);return{kind:"schema",type:"enum",reference:$t,expects:k(e.map(f),"|"),async:!1,enum:t,options:e,message:n,get"~standard"(){return p(this)},"~run"(s,u){return this.options.includes(s.value)?s.typed=!0:r(this,"type",s,u),s}}}function Ft(t,n){return{kind:"schema",type:"exact_optional",reference:Ft,expects:t.expects,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return this.wrapped["~run"](e,s)}}}function zt(t,n){return{kind:"schema",type:"exact_optional",reference:zt,expects:t.expects,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return this.wrapped["~run"](e,s)}}}function Gt(t){return{kind:"schema",type:"file",reference:Gt,expects:"File",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof File?n.typed=!0:r(this,"type",n,e),n}}}function Ut(t){return{kind:"schema",type:"function",reference:Ut,expects:"Function",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="function"?n.typed=!0:r(this,"type",n,e),n}}}function Xt(t,n){return{kind:"schema",type:"instance",reference:Xt,expects:t.name,async:!1,class:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value instanceof this.class?e.typed=!0:r(this,"type",e,s),e}}}function R(t,n){if(typeof t==typeof n){if(t===n||t instanceof Date&&n instanceof Date&&+t==+n)return{value:t};if(t&&n&&t.constructor===Object&&n.constructor===Object){for(let e in n)if(e in t){let s=R(t[e],n[e]);if(s.issue)return s;t[e]=s.value}else t[e]=n[e];return{value:t}}if(Array.isArray(t)&&Array.isArray(n)&&t.length===n.length){for(let e=0;e<t.length;e++){let s=R(t[e],n[e]);if(s.issue)return s;t[e]=s.value}return{value:t}}}return{issue:!0}}function Ht(t,n){return{kind:"schema",type:"intersect",reference:Ht,expects:k(t.map(e=>e.expects),"&"),async:!1,options:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){if(this.options.length){let u=e.value,o;e.typed=!0;for(let a of this.options){let I=a["~run"]({value:u},s);if(I.issues&&(e.issues?e.issues.push(...I.issues):e.issues=I.issues,s.abortEarly)){e.typed=!1;break}I.typed||(e.typed=!1),e.typed&&(o?o.push(I.value):o=[I.value])}if(e.typed){e.value=o[0];for(let a=1;a<o.length;a++){let I=R(e.value,o[a]);if(I.issue){r(this,"type",e,s,{received:"unknown"});break}e.value=I.value}}}else r(this,"type",e,s);return e}}}function Jt(t,n){return{kind:"schema",type:"intersect",reference:Jt,expects:k(t.map(e=>e.expects),"&"),async:!0,options:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){if(this.options.length){let u=e.value,o;e.typed=!0;let a=await Promise.all(this.options.map(I=>I["~run"]({value:u},s)));for(let I of a){if(I.issues&&(e.issues?e.issues.push(...I.issues):e.issues=I.issues,s.abortEarly)){e.typed=!1;break}I.typed||(e.typed=!1),e.typed&&(o?o.push(I.value):o=[I.value])}if(e.typed){e.value=o[0];for(let I=1;I<o.length;I++){let i=R(e.value,o[I]);if(i.issue){r(this,"type",e,s,{received:"unknown"});break}e.value=i.value}}}else r(this,"type",e,s);return e}}}function Zt(t){return{kind:"schema",type:"lazy",reference:Zt,expects:"unknown",async:!1,getter:t,get"~standard"(){return p(this)},"~run"(n,e){return this.getter(n.value)["~run"](n,e)}}}function Qt(t){return{kind:"schema",type:"lazy",reference:Qt,expects:"unknown",async:!0,getter:t,get"~standard"(){return p(this)},async"~run"(n,e){return(await this.getter(n.value))["~run"](n,e)}}}function Yt(t,n){return{kind:"schema",type:"literal",reference:Yt,expects:f(t),async:!1,literal:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===this.literal?e.typed=!0:r(this,"type",e,s),e}}}function es(t,n){return{kind:"schema",type:"loose_object",reference:es,expects:"Object",async:!1,entries:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let a=this.entries[o];if(o in u||(a.type==="exact_optional"||a.type==="optional"||a.type==="nullish")&&a.default!==void 0){let I=o in u?u[o]:y(a),i=a["~run"]({value:I},s);if(i.issues){let m={type:"object",origin:"value",input:u,key:o,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value[o]=i.value}else if(a.fallback!==void 0)e.value[o]=l(a);else if(a.type!=="exact_optional"&&a.type!=="optional"&&a.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${o}"`,path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]}),s.abortEarly))break}if(!e.issues||!s.abortEarly)for(let o in u)g(u,o)&&!(o in this.entries)&&(e.value[o]=u[o])}else r(this,"type",e,s);return e}}}function ns(t,n){return{kind:"schema",type:"loose_object",reference:ns,expects:"Object",async:!0,entries:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([a,I])=>{if(a in u||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in u?u[a]:await y(I);return[a,i,I,await I["~run"]({value:i},s)]}return[a,u[a],I,null]}));for(let[a,I,i,m]of o)if(m){if(m.issues){let c={type:"object",origin:"value",input:u,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],e.issues?.push(T);if(e.issues||(e.issues=m.issues),s.abortEarly){e.typed=!1;break}}m.typed||(e.typed=!1),e.value[a]=m.value}else if(i.fallback!==void 0)e.value[a]=await l(i);else if(i.type!=="exact_optional"&&i.type!=="optional"&&i.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:u,key:a,value:I}]}),s.abortEarly))break;if(!e.issues||!s.abortEarly)for(let a in u)g(u,a)&&!(a in this.entries)&&(e.value[a]=u[a])}else r(this,"type",e,s);return e}}}function ts(t,n){return{kind:"schema",type:"loose_tuple",reference:ts,expects:"Array",async:!1,items:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let a=u[o],I=this.items[o]["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}if(!e.issues||!s.abortEarly)for(let o=this.items.length;o<u.length;o++)e.value.push(u[o])}else r(this,"type",e,s);return e}}}function ss(t,n){return{kind:"schema",type:"loose_tuple",reference:ss,expects:"Array",async:!0,items:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(a,I)=>{let i=u[I];return[I,i,await a["~run"]({value:i},s)]}));for(let[a,I,i]of o){if(i.issues){let m={type:"array",origin:"value",input:u,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value.push(i.value)}if(!e.issues||!s.abortEarly)for(let a=this.items.length;a<u.length;a++)e.value.push(u[a])}else r(this,"type",e,s);return e}}}function us(t,n,e){return{kind:"schema",type:"map",reference:us,expects:"Map",async:!1,key:t,value:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o instanceof Map){s.typed=!0,s.value=new Map;for(let[a,I]of o){let i=this.key["~run"]({value:a},u);if(i.issues){let c={type:"map",origin:"key",input:o,key:a,value:I};for(let T of i.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}let m=this.value["~run"]({value:I},u);if(m.issues){let c={type:"map",origin:"value",input:o,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}(!i.typed||!m.typed)&&(s.typed=!1),s.value.set(i.value,m.value)}}else r(this,"type",s,u);return s}}}function rs(t,n,e){return{kind:"schema",type:"map",reference:rs,expects:"Map",async:!0,key:t,value:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o instanceof Map){s.typed=!0,s.value=new Map;let a=await Promise.all([...o].map(([I,i])=>Promise.all([I,i,this.key["~run"]({value:I},u),this.value["~run"]({value:i},u)])));for(let[I,i,m,c]of a){if(m.issues){let T={type:"map",origin:"key",input:o,key:I,value:i};for(let d of m.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}if(c.issues){let T={type:"map",origin:"value",input:o,key:I,value:i};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}(!m.typed||!c.typed)&&(s.typed=!1),s.value.set(m.value,c.value)}}else r(this,"type",s,u);return s}}}function os(t){return{kind:"schema",type:"nan",reference:os,expects:"NaN",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return Number.isNaN(n.value)?n.typed=!0:r(this,"type",n,e),n}}}function as(t){return{kind:"schema",type:"never",reference:as,expects:"never",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return r(this,"type",n,e),n}}}function is(t,n){return{kind:"schema",type:"non_nullable",reference:is,expects:"!null",async:!1,wrapped:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value!==null&&(e=this.wrapped["~run"](e,s)),e.value===null&&r(this,"type",e,s),e}}}function Is(t,n){return{kind:"schema",type:"non_nullable",reference:Is,expects:"!null",async:!0,wrapped:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value!==null&&(e=await this.wrapped["~run"](e,s)),e.value===null&&r(this,"type",e,s),e}}}function ps(t,n){return{kind:"schema",type:"non_nullish",reference:ps,expects:"(!null & !undefined)",async:!1,wrapped:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===null||e.value===void 0||(e=this.wrapped["~run"](e,s)),(e.value===null||e.value===void 0)&&r(this,"type",e,s),e}}}function ms(t,n){return{kind:"schema",type:"non_nullish",reference:ms,expects:"(!null & !undefined)",async:!0,wrapped:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===null||e.value===void 0||(e=await this.wrapped["~run"](e,s)),(e.value===null||e.value===void 0)&&r(this,"type",e,s),e}}}function N(t,n){return{kind:"schema",type:"non_optional",reference:N,expects:"!undefined",async:!1,wrapped:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value!==void 0&&(e=this.wrapped["~run"](e,s)),e.value===void 0&&r(this,"type",e,s),e}}}function C(t,n){return{kind:"schema",type:"non_optional",reference:C,expects:"!undefined",async:!0,wrapped:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value!==void 0&&(e=await this.wrapped["~run"](e,s)),e.value===void 0&&r(this,"type",e,s),e}}}function cs(t){return{kind:"schema",type:"null",reference:cs,expects:"null",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value===null?n.typed=!0:r(this,"type",n,e),n}}}function Ts(t,n){return{kind:"schema",type:"nullable",reference:Ts,expects:`(${t.expects} | null)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===null&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===null)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function ds(t,n){return{kind:"schema",type:"nullable",reference:ds,expects:`(${t.expects} | null)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===null&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===null)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function fs(t,n){return{kind:"schema",type:"nullish",reference:fs,expects:`(${t.expects} | null | undefined)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return(e.value===null||e.value===void 0)&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===null||e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function ys(t,n){return{kind:"schema",type:"nullish",reference:ys,expects:`(${t.expects} | null | undefined)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return(e.value===null||e.value===void 0)&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===null||e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function ls(t){return{kind:"schema",type:"number",reference:ls,expects:"number",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="number"&&!isNaN(n.value)?n.typed=!0:r(this,"type",n,e),n}}}function hs(t,n){return{kind:"schema",type:"object",reference:hs,expects:"Object",async:!1,entries:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let a=this.entries[o];if(o in u||(a.type==="exact_optional"||a.type==="optional"||a.type==="nullish")&&a.default!==void 0){let I=o in u?u[o]:y(a),i=a["~run"]({value:I},s);if(i.issues){let m={type:"object",origin:"value",input:u,key:o,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value[o]=i.value}else if(a.fallback!==void 0)e.value[o]=l(a);else if(a.type!=="exact_optional"&&a.type!=="optional"&&a.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${o}"`,path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]}),s.abortEarly))break}}else r(this,"type",e,s);return e}}}function ks(t,n){return{kind:"schema",type:"object",reference:ks,expects:"Object",async:!0,entries:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([a,I])=>{if(a in u||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in u?u[a]:await y(I);return[a,i,I,await I["~run"]({value:i},s)]}return[a,u[a],I,null]}));for(let[a,I,i,m]of o)if(m){if(m.issues){let c={type:"object",origin:"value",input:u,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],e.issues?.push(T);if(e.issues||(e.issues=m.issues),s.abortEarly){e.typed=!1;break}}m.typed||(e.typed=!1),e.value[a]=m.value}else if(i.fallback!==void 0)e.value[a]=await l(i);else if(i.type!=="exact_optional"&&i.type!=="optional"&&i.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:u,key:a,value:I}]}),s.abortEarly))break}else r(this,"type",e,s);return e}}}function xs(t,n,e){return{kind:"schema",type:"object_with_rest",reference:xs,expects:"Object",async:!1,entries:t,rest:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};for(let a in this.entries){let I=this.entries[a];if(a in o||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in o?o[a]:y(I),m=I["~run"]({value:i},u);if(m.issues){let c={type:"object",origin:"value",input:o,key:a,value:i};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}m.typed||(s.typed=!1),s.value[a]=m.value}else if(I.fallback!==void 0)s.value[a]=l(I);else if(I.type!=="exact_optional"&&I.type!=="optional"&&I.type!=="nullish"&&(r(this,"key",s,u,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:o,key:a,value:o[a]}]}),u.abortEarly))break}if(!s.issues||!u.abortEarly){for(let a in o)if(g(o,a)&&!(a in this.entries)){let I=this.rest["~run"]({value:o[a]},u);if(I.issues){let i={type:"object",origin:"value",input:o,key:a,value:o[a]};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],s.issues?.push(m);if(s.issues||(s.issues=I.issues),u.abortEarly){s.typed=!1;break}}I.typed||(s.typed=!1),s.value[a]=I.value}}}else r(this,"type",s,u);return s}}}function Os(t,n,e){return{kind:"schema",type:"object_with_rest",reference:Os,expects:"Object",async:!0,entries:t,rest:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};let[a,I]=await Promise.all([Promise.all(Object.entries(this.entries).map(async([i,m])=>{if(i in o||(m.type==="exact_optional"||m.type==="optional"||m.type==="nullish")&&m.default!==void 0){let c=i in o?o[i]:await y(m);return[i,c,m,await m["~run"]({value:c},u)]}return[i,o[i],m,null]})),Promise.all(Object.entries(o).filter(([i])=>g(o,i)&&!(i in this.entries)).map(async([i,m])=>[i,m,await this.rest["~run"]({value:m},u)]))]);for(let[i,m,c,T]of a)if(T){if(T.issues){let d={type:"object",origin:"value",input:o,key:i,value:m};for(let O of T.issues)O.path?O.path.unshift(d):O.path=[d],s.issues?.push(O);if(s.issues||(s.issues=T.issues),u.abortEarly){s.typed=!1;break}}T.typed||(s.typed=!1),s.value[i]=T.value}else if(c.fallback!==void 0)s.value[i]=await l(c);else if(c.type!=="exact_optional"&&c.type!=="optional"&&c.type!=="nullish"&&(r(this,"key",s,u,{input:void 0,expected:`"${i}"`,path:[{type:"object",origin:"key",input:o,key:i,value:m}]}),u.abortEarly))break;if(!s.issues||!u.abortEarly)for(let[i,m,c]of I){if(c.issues){let T={type:"object",origin:"value",input:o,key:i,value:m};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}c.typed||(s.typed=!1),s.value[i]=c.value}}else r(this,"type",s,u);return s}}}function L(t,n){return{kind:"schema",type:"optional",reference:L,expects:`(${t.expects} | undefined)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function K(t,n){return{kind:"schema",type:"optional",reference:K,expects:`(${t.expects} | undefined)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function $(t,n){return{kind:"schema",type:"picklist",reference:$,expects:k(t.map(f),"|"),async:!1,options:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return this.options.includes(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function gs(t){return{kind:"schema",type:"promise",reference:gs,expects:"Promise",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof Promise?n.typed=!0:r(this,"type",n,e),n}}}function ws(t,n,e){return{kind:"schema",type:"record",reference:ws,expects:"Object",async:!1,key:t,value:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};for(let a in o)if(g(o,a)){let I=o[a],i=this.key["~run"]({value:a},u);if(i.issues){let c={type:"object",origin:"key",input:o,key:a,value:I};for(let T of i.issues)T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}let m=this.value["~run"]({value:I},u);if(m.issues){let c={type:"object",origin:"value",input:o,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}(!i.typed||!m.typed)&&(s.typed=!1),i.typed&&(s.value[i.value]=m.value)}}else r(this,"type",s,u);return s}}}function Ss(t,n,e){return{kind:"schema",type:"record",reference:Ss,expects:"Object",async:!0,key:t,value:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};let a=await Promise.all(Object.entries(o).filter(([I])=>g(o,I)).map(([I,i])=>Promise.all([I,i,this.key["~run"]({value:I},u),this.value["~run"]({value:i},u)])));for(let[I,i,m,c]of a){if(m.issues){let T={type:"object",origin:"key",input:o,key:I,value:i};for(let d of m.issues)d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}if(c.issues){let T={type:"object",origin:"value",input:o,key:I,value:i};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}(!m.typed||!c.typed)&&(s.typed=!1),m.typed&&(s.value[m.value]=c.value)}}else r(this,"type",s,u);return s}}}function Bs(t,n){return{kind:"schema",type:"set",reference:Bs,expects:"Set",async:!1,value:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u instanceof Set){e.typed=!0,e.value=new Set;for(let o of u){let a=this.value["~run"]({value:o},s);if(a.issues){let I={type:"set",origin:"value",input:u,key:null,value:o};for(let i of a.issues)i.path?i.path.unshift(I):i.path=[I],e.issues?.push(i);if(e.issues||(e.issues=a.issues),s.abortEarly){e.typed=!1;break}}a.typed||(e.typed=!1),e.value.add(a.value)}}else r(this,"type",e,s);return e}}}function As(t,n){return{kind:"schema",type:"set",reference:As,expects:"Set",async:!0,value:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u instanceof Set){e.typed=!0,e.value=new Set;let o=await Promise.all([...u].map(async a=>[a,await this.value["~run"]({value:a},s)]));for(let[a,I]of o){if(I.issues){let i={type:"set",origin:"value",input:u,key:null,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.add(I.value)}}else r(this,"type",e,s);return e}}}function bs(t,n){return{kind:"schema",type:"strict_object",reference:bs,expects:"Object",async:!1,entries:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let a=this.entries[o];if(o in u||(a.type==="exact_optional"||a.type==="optional"||a.type==="nullish")&&a.default!==void 0){let I=o in u?u[o]:y(a),i=a["~run"]({value:I},s);if(i.issues){let m={type:"object",origin:"value",input:u,key:o,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value[o]=i.value}else if(a.fallback!==void 0)e.value[o]=l(a);else if(a.type!=="exact_optional"&&a.type!=="optional"&&a.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${o}"`,path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]}),s.abortEarly))break}if(!e.issues||!s.abortEarly){for(let o in u)if(!(o in this.entries)){r(this,"key",e,s,{input:o,expected:"never",path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]});break}}}else r(this,"type",e,s);return e}}}function Es(t,n){return{kind:"schema",type:"strict_object",reference:Es,expects:"Object",async:!0,entries:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([a,I])=>{if(a in u||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in u?u[a]:await y(I);return[a,i,I,await I["~run"]({value:i},s)]}return[a,u[a],I,null]}));for(let[a,I,i,m]of o)if(m){if(m.issues){let c={type:"object",origin:"value",input:u,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],e.issues?.push(T);if(e.issues||(e.issues=m.issues),s.abortEarly){e.typed=!1;break}}m.typed||(e.typed=!1),e.value[a]=m.value}else if(i.fallback!==void 0)e.value[a]=await l(i);else if(i.type!=="exact_optional"&&i.type!=="optional"&&i.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:u,key:a,value:I}]}),s.abortEarly))break;if(!e.issues||!s.abortEarly){for(let a in u)if(!(a in this.entries)){r(this,"key",e,s,{input:a,expected:"never",path:[{type:"object",origin:"key",input:u,key:a,value:u[a]}]});break}}}else r(this,"type",e,s);return e}}}function Ms(t,n){return{kind:"schema",type:"strict_tuple",reference:Ms,expects:"Array",async:!1,items:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let a=u[o],I=this.items[o]["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}!(e.issues&&s.abortEarly)&&this.items.length<u.length&&r(this,"type",e,s,{input:u[this.items.length],expected:"never",path:[{type:"array",origin:"value",input:u,key:this.items.length,value:u[this.items.length]}]})}else r(this,"type",e,s);return e}}}function Ps(t,n){return{kind:"schema",type:"strict_tuple",reference:Ps,expects:"Array",async:!0,items:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(a,I)=>{let i=u[I];return[I,i,await a["~run"]({value:i},s)]}));for(let[a,I,i]of o){if(i.issues){let m={type:"array",origin:"value",input:u,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value.push(i.value)}!(e.issues&&s.abortEarly)&&this.items.length<u.length&&r(this,"type",e,s,{input:u[this.items.length],expected:"never",path:[{type:"array",origin:"value",input:u,key:this.items.length,value:u[this.items.length]}]})}else r(this,"type",e,s);return e}}}function Rs(t){return{kind:"schema",type:"string",reference:Rs,expects:"string",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="string"?n.typed=!0:r(this,"type",n,e),n}}}function js(t){return{kind:"schema",type:"symbol",reference:js,expects:"symbol",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="symbol"?n.typed=!0:r(this,"type",n,e),n}}}function vs(t,n){return{kind:"schema",type:"tuple",reference:vs,expects:"Array",async:!1,items:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let a=u[o],I=this.items[o]["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}}else r(this,"type",e,s);return e}}}function qs(t,n){return{kind:"schema",type:"tuple",reference:qs,expects:"Array",async:!0,items:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(a,I)=>{let i=u[I];return[I,i,await a["~run"]({value:i},s)]}));for(let[a,I,i]of o){if(i.issues){let m={type:"array",origin:"value",input:u,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value.push(i.value)}}else r(this,"type",e,s);return e}}}function _s(t,n,e){return{kind:"schema",type:"tuple_with_rest",reference:_s,expects:"Array",async:!1,items:t,rest:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(Array.isArray(o)){s.typed=!0,s.value=[];for(let a=0;a<this.items.length;a++){let I=o[a],i=this.items[a]["~run"]({value:I},u);if(i.issues){let m={type:"array",origin:"value",input:o,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],s.issues?.push(c);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}i.typed||(s.typed=!1),s.value.push(i.value)}if(!s.issues||!u.abortEarly)for(let a=this.items.length;a<o.length;a++){let I=o[a],i=this.rest["~run"]({value:I},u);if(i.issues){let m={type:"array",origin:"value",input:o,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],s.issues?.push(c);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}i.typed||(s.typed=!1),s.value.push(i.value)}}else r(this,"type",s,u);return s}}}function Ds(t,n,e){return{kind:"schema",type:"tuple_with_rest",reference:Ds,expects:"Array",async:!0,items:t,rest:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(Array.isArray(o)){s.typed=!0,s.value=[];let[a,I]=await Promise.all([Promise.all(this.items.map(async(i,m)=>{let c=o[m];return[m,c,await i["~run"]({value:c},u)]})),Promise.all(o.slice(this.items.length).map(async(i,m)=>[m+this.items.length,i,await this.rest["~run"]({value:i},u)]))]);for(let[i,m,c]of a){if(c.issues){let T={type:"array",origin:"value",input:o,key:i,value:m};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}c.typed||(s.typed=!1),s.value.push(c.value)}if(!s.issues||!u.abortEarly)for(let[i,m,c]of I){if(c.issues){let T={type:"array",origin:"value",input:o,key:i,value:m};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}c.typed||(s.typed=!1),s.value.push(c.value)}}else r(this,"type",s,u);return s}}}function Ws(t){return{kind:"schema",type:"undefined",reference:Ws,expects:"undefined",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value===void 0?n.typed=!0:r(this,"type",n,e),n}}}function Vs(t,n){return{kind:"schema",type:"undefinedable",reference:Vs,expects:`(${t.expects} | undefined)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function Ns(t,n){return{kind:"schema",type:"undefinedable",reference:Ns,expects:`(${t.expects} | undefined)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function j(t){let n;if(t)for(let e of t)n?n.push(...e.issues):n=e.issues;return n}function Cs(t,n){return{kind:"schema",type:"union",reference:Cs,expects:k(t.map(e=>e.expects),"|"),async:!1,options:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u,o,a;for(let I of this.options){let i=I["~run"]({value:e.value},s);if(i.typed)if(i.issues)o?o.push(i):o=[i];else{u=i;break}else a?a.push(i):a=[i]}if(u)return u;if(o){if(o.length===1)return o[0];r(this,"type",e,s,{issues:j(o)}),e.typed=!0}else{if(a?.length===1)return a[0];r(this,"type",e,s,{issues:j(a)})}return e}}}function Ls(t,n){return{kind:"schema",type:"union",reference:Ls,expects:k(t.map(e=>e.expects),"|"),async:!0,options:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u,o,a;for(let I of this.options){let i=await I["~run"]({value:e.value},s);if(i.typed)if(i.issues)o?o.push(i):o=[i];else{u=i;break}else a?a.push(i):a=[i]}if(u)return u;if(o){if(o.length===1)return o[0];r(this,"type",e,s,{issues:j(o)}),e.typed=!0}else{if(a?.length===1)return a[0];r(this,"type",e,s,{issues:j(a)})}return e}}}function Ks(){return{kind:"schema",type:"unknown",reference:Ks,expects:"unknown",async:!1,get"~standard"(){return p(this)},"~run"(t){return t.typed=!0,t}}}function $s(t,n,e){return{kind:"schema",type:"variant",reference:$s,expects:"Object",async:!1,key:t,options:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){let a,I=0,i=this.key,m=[],c=(T,d)=>{for(let O of T.options){if(O.type==="variant")c(O,new Set(d).add(O.key));else{let v=!0,S=0;for(let h of d){let B=O.entries[h];if(h in o?B["~run"]({typed:!1,value:o[h]},u).issues:B.type!=="exact_optional"&&B.type!=="optional"&&B.type!=="nullish"){v=!1,i!==h&&(I<S||I===S&&h in o&&!(i in o))&&(I=S,i=h,m=[]),i===h&&m.push(O.entries[h].expects);break}S++}if(v){let h=O["~run"]({value:o},u);(!a||!a.typed&&h.typed)&&(a=h)}}if(a&&!a.issues)break}};if(c(this,new Set([this.key])),a)return a;r(this,"type",s,u,{input:o[i],expected:k(m,"|"),path:[{type:"object",origin:"value",input:o,key:i,value:o[i]}]})}else r(this,"type",s,u);return s}}}function Fs(t,n,e){return{kind:"schema",type:"variant",reference:Fs,expects:"Object",async:!0,key:t,options:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){let a,I=0,i=this.key,m=[],c=async(T,d)=>{for(let O of T.options){if(O.type==="variant")await c(O,new Set(d).add(O.key));else{let v=!0,S=0;for(let h of d){let B=O.entries[h];if(h in o?(await B["~run"]({typed:!1,value:o[h]},u)).issues:B.type!=="exact_optional"&&B.type!=="optional"&&B.type!=="nullish"){v=!1,i!==h&&(I<S||I===S&&h in o&&!(i in o))&&(I=S,i=h,m=[]),i===h&&m.push(O.entries[h].expects);break}S++}if(v){let h=await O["~run"]({value:o},u);(!a||!a.typed&&h.typed)&&(a=h)}}if(a&&!a.issues)break}};if(await c(this,new Set([this.key])),a)return a;r(this,"type",s,u,{input:o[i],expected:k(m,"|"),path:[{type:"object",origin:"value",input:o,key:i,value:o[i]}]})}else r(this,"type",s,u);return s}}}function zs(t){return{kind:"schema",type:"void",reference:zs,expects:"void",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value===void 0?n.typed=!0:r(this,"type",n,e),n}}}function Su(t,n){return $(Object.keys(t.entries),n)}function Bu(t,n){let e={...t.entries};for(let s of n)delete e[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function Me(t,n,e){let s=t["~run"]({value:n},w(e));if(s.issues)throw new x(s.issues);return s.value}async function Pe(t,n,e){let s=await t["~run"]({value:n},w(e));if(s.issues)throw new x(s.issues);return s.value}function Au(t,n){let e=s=>Me(t,s,n);return e.schema=t,e.config=n,e}function bu(t,n){let e=s=>Pe(t,s,n);return e.schema=t,e.config=n,e}function Eu(t,n){let e={};for(let s in t.entries)e[s]=!n||n.includes(s)?L(t.entries[s]):t.entries[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function Mu(t,n){let e={};for(let s in t.entries)e[s]=!n||n.includes(s)?K(t.entries[s]):t.entries[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function Pu(t,n){let e={};for(let s of n)e[s]=t.entries[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function Ru(...t){return{...t[0],pipe:t,get"~standard"(){return p(this)},"~run"(n,e){for(let s of t)if(s.kind!=="metadata"){if(n.issues&&(s.kind==="schema"||s.kind==="transformation")){n.typed=!1;break}(!n.issues||!e.abortEarly&&!e.abortPipeEarly)&&(n=s["~run"](n,e))}return n}}}function ju(...t){return{...t[0],pipe:t,async:!0,get"~standard"(){return p(this)},async"~run"(n,e){for(let s of t)if(s.kind!=="metadata"){if(n.issues&&(s.kind==="schema"||s.kind==="transformation")){n.typed=!1;break}(!n.issues||!e.abortEarly&&!e.abortPipeEarly)&&(n=await s["~run"](n,e))}return n}}}function vu(t,n,e){let s=Array.isArray(n)?n:void 0,u=Array.isArray(n)?e:n,o={};for(let a in t.entries)o[a]=!s||s.includes(a)?N(t.entries[a],u):t.entries[a];return{...t,entries:o,get"~standard"(){return p(this)}}}function qu(t,n,e){let s=Array.isArray(n)?n:void 0,u=Array.isArray(n)?e:n,o={};for(let a in t.entries)o[a]=!s||s.includes(a)?C(t.entries[a],u):t.entries[a];return{...t,entries:o,get"~standard"(){return p(this)}}}function Re(t,n,e){let s=t["~run"]({value:n},w(e));return{typed:s.typed,success:!s.issues,output:s.value,issues:s.issues}}async function je(t,n,e){let s=await t["~run"]({value:n},w(e));return{typed:s.typed,success:!s.issues,output:s.value,issues:s.issues}}function _u(t,n){let e=s=>Re(t,s,n);return e.schema=t,e.config=n,e}function Du(t,n){let e=s=>je(t,s,n);return e.schema=t,e.config=n,e}function Wu(t){return t.wrapped}0&&(module.exports={BASE64_REGEX,BIC_REGEX,CUID2_REGEX,DECIMAL_REGEX,DIGITS_REGEX,EMAIL_REGEX,EMOJI_REGEX,HEXADECIMAL_REGEX,HEX_COLOR_REGEX,IMEI_REGEX,IPV4_REGEX,IPV6_REGEX,IP_REGEX,ISO_DATE_REGEX,ISO_DATE_TIME_REGEX,ISO_TIMESTAMP_REGEX,ISO_TIME_REGEX,ISO_TIME_SECOND_REGEX,ISO_WEEK_REGEX,MAC48_REGEX,MAC64_REGEX,MAC_REGEX,NANO_ID_REGEX,OCTAL_REGEX,RFC_EMAIL_REGEX,SLUG_REGEX,ULID_REGEX,UUID_REGEX,ValiError,_addIssue,_getByteCount,_getGraphemeCount,_getStandardProps,_getWordCount,_isLuhnAlgo,_isValidObjectKey,_joinExpects,_stringify,any,args,argsAsync,array,arrayAsync,assert,awaitAsync,base64,bic,bigint,blob,boolean,brand,bytes,check,checkAsync,checkItems,checkItemsAsync,config,creditCard,cuid2,custom,customAsync,date,decimal,deleteGlobalConfig,deleteGlobalMessage,deleteSchemaMessage,deleteSpecificMessage,description,digits,email,emoji,empty,endsWith,entriesFromList,entriesFromObjects,enum:null,enum_,everyItem,exactOptional,exactOptionalAsync,excludes,fallback,fallbackAsync,file,filterItems,findItem,finite,flatten,forward,forwardAsync,function:null,function_,getDefault,getDefaults,getDefaultsAsync,getDotPath,getFallback,getFallbacks,getFallbacksAsync,getGlobalConfig,getGlobalMessage,getSchemaMessage,getSpecificMessage,graphemes,gtValue,hash,hexColor,hexadecimal,imei,includes,instance,integer,intersect,intersectAsync,ip,ipv4,ipv6,is,isOfKind,isOfType,isValiError,isoDate,isoDateTime,isoTime,isoTimeSecond,isoTimestamp,isoWeek,keyof,lazy,lazyAsync,length,literal,looseObject,looseObjectAsync,looseTuple,looseTupleAsync,ltValue,mac,mac48,mac64,map,mapAsync,mapItems,maxBytes,maxGraphemes,maxLength,maxSize,maxValue,maxWords,metadata,mimeType,minBytes,minGraphemes,minLength,minSize,minValue,minWords,multipleOf,nan,nanoid,never,nonEmpty,nonNullable,nonNullableAsync,nonNullish,nonNullishAsync,nonOptional,nonOptionalAsync,normalize,notBytes,notGraphemes,notLength,notSize,notValue,notValues,notWords,null:null,null_,nullable,nullableAsync,nullish,nullishAsync,number,object,objectAsync,objectWithRest,objectWithRestAsync,octal,omit,optional,optionalAsync,parse,parseAsync,parser,parserAsync,partial,partialAsync,partialCheck,partialCheckAsync,pick,picklist,pipe,pipeAsync,promise,rawCheck,rawCheckAsync,rawTransform,rawTransformAsync,readonly,record,recordAsync,reduceItems,regex,required,requiredAsync,returns,returnsAsync,rfcEmail,safeInteger,safeParse,safeParseAsync,safeParser,safeParserAsync,set,setAsync,setGlobalConfig,setGlobalMessage,setSchemaMessage,setSpecificMessage,size,slug,someItem,sortItems,startsWith,strictObject,strictObjectAsync,strictTuple,strictTupleAsync,string,symbol,title,toLowerCase,toMaxValue,toMinValue,toUpperCase,transform,transformAsync,trim,trimEnd,trimStart,tuple,tupleAsync,tupleWithRest,tupleWithRestAsync,ulid,undefined,undefined_,undefinedable,undefinedableAsync,union,unionAsync,unknown,unwrap,url,uuid,value,values,variant,variantAsync,void:null,void_,words});
