import { Injectable } from '@nestjs/common';

@Injectable()
export class TelegramService {
  private readonly botToken: string;
  private readonly baseUrl: string;

  constructor() {
    this.botToken = process.env.TELEGRAM_BOT_TOKEN || '';
    this.baseUrl = `https://api.telegram.org/bot${this.botToken}`;
  }

  async getUserProfilePhoto(userId: string): Promise<string | null> {
    try {
      if (!this.botToken) {
        console.warn('TELEGRAM_BOT_TOKEN not set, cannot get user photo');
        return null;
      }

      // Получаем фотографии профиля пользователя
      const response = await fetch(`${this.baseUrl}/getUserProfilePhotos?user_id=${userId}&limit=1`);
      
      if (!response.ok) {
        console.error('Failed to get user profile photos:', response.statusText);
        return null;
      }

      const data = await response.json();
      
      if (!data.ok || !data.result.photos || data.result.photos.length === 0) {
        console.log('No profile photos found for user:', userId);
        return null;
      }

      // Берем самую большую версию первой фотографии
      const photo = data.result.photos[0];
      const largestPhoto = photo[photo.length - 1]; // Последний элемент - самый большой размер
      
      // Получаем информацию о файле
      const fileResponse = await fetch(`${this.baseUrl}/getFile?file_id=${largestPhoto.file_id}`);
      
      if (!fileResponse.ok) {
        console.error('Failed to get file info:', fileResponse.statusText);
        return null;
      }

      const fileData = await fileResponse.json();
      
      if (!fileData.ok || !fileData.result.file_path) {
        console.error('Invalid file data received');
        return null;
      }

      // Формируем URL для скачивания файла
      const photoUrl = `https://api.telegram.org/file/bot${this.botToken}/${fileData.result.file_path}`;
      
      return photoUrl;
    } catch (error) {
      console.error('Error getting user profile photo:', error);
      return null;
    }
  }
}
