/* Withdraw Component Styles */
.withdraw {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.withdraw-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 16px;
  color: rgba(255, 255, 255, 0.7);
}

/* Header */
.withdraw-header {
  text-align: center;
  margin-bottom: 10px;
}

.withdraw-header h1 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 24px;
  margin-bottom: 8px;
  color: #fff;
}

.withdraw-header p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin-bottom: 16px;
}

.balance-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid rgba(0, 255, 136, 0.3);
  border-radius: 12px;
  font-weight: 600;
  color: var(--primary-green);
  max-width: 200px;
  margin: 0 auto;
}

/* Gift Selection */
.gift-selection {
  padding: 20px;
}

.gift-selection h2 {
  font-size: 18px;
  margin-bottom: 16px;
  color: #fff;
}

.no-gifts {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.7);
}

.no-gifts p {
  margin: 8px 0;
}

.min-amount {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.gifts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 12px;
}

.gift-card {
  position: relative;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.gift-card:hover {
  border-color: rgba(255, 215, 0, 0.5);
  background: rgba(255, 215, 0, 0.05);
}

.gift-card.selected {
  border-color: var(--primary-gold);
  background: rgba(255, 215, 0, 0.1);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.gift-image {
  width: 60px;
  height: 60px;
  margin: 0 auto 12px;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
}

.gift-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gift-info h3 {
  font-size: 14px;
  margin-bottom: 8px;
  color: #fff;
  word-break: break-word;
}

.gift-price {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.selected-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  color: var(--primary-gold);
  font-size: 16px;
}

/* Recipient Section */
.recipient-section {
  padding: 20px;
}

.recipient-section h2 {
  font-size: 18px;
  margin-bottom: 16px;
  color: #fff;
}

.input-wrapper {
  position: relative;
  margin-bottom: 20px;
}

.input-wrapper .input {
  padding-left: 40px;
}

.input-wrapper .input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.5);
}

/* Withdraw Summary */
.withdraw-summary {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item span:first-child {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.summary-item span:last-child {
  color: #fff;
  font-weight: 600;
  font-size: 14px;
}

/* Withdraw Button */
.withdraw-button {
  width: 100%;
  padding: 16px;
  font-size: 16px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.withdraw-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Withdraw Result */
.withdraw-result {
  padding: 20px;
  text-align: center;
  border: 2px solid;
  border-radius: 12px;
}

.withdraw-result.success {
  border-color: var(--primary-green);
  background: rgba(0, 255, 136, 0.1);
}

.withdraw-result.error {
  border-color: var(--danger-red);
  background: rgba(255, 71, 87, 0.1);
}

.result-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.result-content h3 {
  font-size: 18px;
  margin-bottom: 12px;
  color: #fff;
}

.result-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.amount-deducted {
  font-weight: 700;
  color: var(--primary-green);
}

/* Withdraw Info */
.withdraw-info {
  padding: 20px;
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.withdraw-info .fa-info-circle {
  font-size: 20px;
  margin-top: 2px;
  flex-shrink: 0;
}

.info-content h3 {
  font-size: 16px;
  margin-bottom: 12px;
  color: #fff;
}

.info-content ul {
  margin: 0;
  padding-left: 20px;
  color: rgba(255, 255, 255, 0.8);
}

.info-content li {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 6px;
}

/* Responsive */
@media (max-width: 480px) {
  .withdraw-header h1 {
    font-size: 20px;
    gap: 8px;
  }
  
  .balance-display {
    padding: 10px 16px;
    font-size: 14px;
  }
  
  .gift-selection,
  .recipient-section,
  .withdraw-result,
  .withdraw-info {
    padding: 16px;
  }
  
  .gifts-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
  }
  
  .gift-card {
    padding: 12px;
  }
  
  .gift-image {
    width: 50px;
    height: 50px;
    margin-bottom: 8px;
  }
  
  .gift-info h3 {
    font-size: 13px;
    margin-bottom: 6px;
  }
  
  .gift-price {
    font-size: 11px;
  }
  
  .withdraw-button {
    padding: 14px;
    font-size: 15px;
  }
  
  .result-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }
  
  .result-content h3 {
    font-size: 16px;
  }
  
  .withdraw-info {
    gap: 12px;
  }
  
  .info-content h3 {
    font-size: 15px;
  }
  
  .info-content li {
    font-size: 13px;
  }
}
