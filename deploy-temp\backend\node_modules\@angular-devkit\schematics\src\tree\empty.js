"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmptyTree = void 0;
const host_tree_1 = require("./host-tree");
class EmptyTree extends host_tree_1.HostTree {
    constructor() {
        super();
    }
}
exports.EmptyTree = EmptyTree;
