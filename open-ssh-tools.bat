@echo off
echo 🚀 PEPE CAS Deployment Helper
echo ============================
echo.
echo Выберите способ подключения к серверу:
echo.
echo 1. Открыть WinSCP (рекомендуется)
echo 2. Открыть PuTTY
echo 3. Показать команды для командной строки
echo.
set /p choice="Введите номер (1-3): "

if "%choice%"=="1" (
    echo Открываем WinSCP...
    echo.
    echo Настройки подключения:
    echo Host: *************
    echo Username: sexuz
    echo Port: 22
    echo.
    echo Загрузите файлы из папки deploy-package на сервер
    start "" "https://winscp.net/eng/download.php"
    pause
)

if "%choice%"=="2" (
    echo Открываем PuTTY...
    echo.
    echo Настройки подключения:
    echo Host: *************
    echo Username: sexuz
    echo Port: 22
    echo.
    start "" "https://www.putty.org/"
    pause
)

if "%choice%"=="3" (
    echo.
    echo Команды для загрузки файлов:
    echo scp -r deploy-package/* sexuz@*************:~/
    echo.
    echo Команды для подключения:
    echo ssh sexuz@*************
    echo.
    echo На сервере выполните:
    echo chmod +x full-setup.sh
    echo ./full-setup.sh
    echo.
    pause
)

echo.
echo После загрузки файлов и подключения к серверу:
echo 1. Выполните: chmod +x full-setup.sh
echo 2. Выполните: ./full-setup.sh
echo 3. Дождитесь завершения установки
echo 4. Откройте https://************* в браузере
echo 5. Протестируйте бота в Telegram
echo.
pause
