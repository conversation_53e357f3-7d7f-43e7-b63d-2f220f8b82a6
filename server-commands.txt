# Команды для выполнения на сервере Yandex Cloud
# IP: *************
# User: sexuz

# 1. Подключение к серверу
ssh sexuz@*************

# 2. Загрузка файлов (выполнить с локальной машины)
scp -r deploy-package/* sexuz@*************:~/

# 3. На сервере - установка и настройка
chmod +x setup.sh
./setup.sh

# 4. Настройка SSL для HTTPS (обязательно для Telegram)
sudo apt install certbot -y

# Получение SSL сертификата (замените your-domain.com на ваш домен)
# Если нет домена, используйте самоподписанный сертификат:
sudo openssl req -x509 -newkey rsa:4096 -keyout /etc/ssl/private/pepe-cas.key -out /etc/ssl/certs/pepe-cas.crt -days 365 -nodes -subj "/C=RU/ST=Moscow/L=Moscow/O=PEPE CAS/CN=*************"

# Обновление конфигурации Nginx для HTTPS
sudo tee /etc/nginx/sites-available/pepe-cas > /dev/null << 'EOF'
server {
    listen 80;
    server_name _;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name _;

    ssl_certificate /etc/ssl/certs/pepe-cas.crt;
    ssl_certificate_key /etc/ssl/private/pepe-cas.key;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF

# Перезагрузка Nginx
sudo nginx -t && sudo systemctl reload nginx

# 5. Проверка статуса
pm2 status
sudo systemctl status nginx

# 6. Настройка Telegram бота с HTTPS URL
curl -X POST "https://api.telegram.org/bot7918026591:AAHrRX3Z1tLtO7aE_gPblArhJMGTv5kixvI/setChatMenuButton" \
-H "Content-Type: application/json" \
-d '{
  "menu_button": {
    "type": "web_app",
    "text": "🎰 Играть",
    "web_app": {
      "url": "https://*************"
    }
  }
}'

# 7. Проверка работы
echo "✅ Приложение должно быть доступно по адресу: https://*************"
echo "📱 Откройте бота в Telegram и нажмите кнопку меню"
