#!/bin/bash

echo "🚀 PEPE CAS Quick Deploy"
echo "========================"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}Выберите платформу для деплоя:${NC}"
echo "1) Railway (рекомендуется для начала)"
echo "2) Render"
echo "3) DigitalOcean VPS"
echo "4) Локальный Docker"

read -p "Введите номер (1-4): " choice

case $choice in
    1)
        echo -e "${GREEN}🚂 Деплой на Railway${NC}"
        echo "1. Зайдите на https://railway.app"
        echo "2. Подключите GitHub репозиторий"
        echo "3. Добавьте переменную TELEGRAM_BOT_TOKEN"
        echo "4. Railway автоматически развернет приложение"
        echo ""
        echo "Ваш URL будет: https://your-app-name.up.railway.app"
        ;;
    2)
        echo -e "${GREEN}🎨 Деплой на Render${NC}"
        echo "1. Зайдите на https://render.com"
        echo "2. Создайте новый Web Service"
        echo "3. Подключите GitHub репозиторий"
        echo "4. Настройте:"
        echo "   - Build Command: npm install && npm run build"
        echo "   - Start Command: npm start"
        echo "5. Добавьте переменную TELEGRAM_BOT_TOKEN"
        ;;
    3)
        echo -e "${GREEN}🌊 Деплой на DigitalOcean VPS${NC}"
        echo "1. Создайте Droplet на https://digitalocean.com"
        echo "2. Подключитесь по SSH"
        echo "3. Клонируйте репозиторий"
        echo "4. Запустите: ./deploy.sh"
        echo ""
        echo "Команды для подключения:"
        echo "ssh root@your-server-ip"
        echo "git clone https://github.com/your-username/your-repo.git"
        echo "cd your-repo"
        echo "./deploy.sh"
        ;;
    4)
        echo -e "${GREEN}🐳 Локальный Docker деплой${NC}"
        if ! command -v docker &> /dev/null; then
            echo -e "${YELLOW}Docker не установлен. Установите Docker Desktop${NC}"
            exit 1
        fi
        
        echo "Запускаем локально..."
        chmod +x deploy.sh
        ./deploy.sh
        ;;
    *)
        echo "Неверный выбор"
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}📱 После деплоя:${NC}"
echo "1. Получите токен бота у @BotFather"
echo "2. Настройте Web App URL в боте"
echo "3. Протестируйте в Telegram"
echo ""
echo -e "${GREEN}✅ Готово! Ваше мини-приложение готово к использованию${NC}"
