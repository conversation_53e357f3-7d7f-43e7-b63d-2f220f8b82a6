import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Coins,
  Plus,
  Play,
  Users,
  Loader2
} from 'lucide-react';
import ApiService from '../../services/ApiService';
import './CoinflipGame.css';

const CoinflipGame = ({ user, balance, updateBalance }) => {
  const [activeTab, setActiveTab] = useState('rooms');
  const [waitingRooms, setWaitingRooms] = useState([]);
  const [stake, setStake] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadWaitingRooms();
  }, []);

  const loadWaitingRooms = async () => {
    try {
      const rooms = await ApiService.getWaitingRooms();
      setWaitingRooms(rooms);
    } catch (error) {
      console.error('Error loading rooms:', error);
    }
  };

  const createRoom = async () => {
    if (!stake || parseFloat(stake) <= 0) {
      alert('Введите корректную сумму ставки');
      return;
    }

    if (parseFloat(stake) > balance) {
      alert('Недостаточно средств');
      return;
    }

    setLoading(true);
    try {
      const room = await ApiService.createCoinflipRoom(user.telegram_id, parseFloat(stake));
      if (room) {
        updateBalance(balance - parseFloat(stake));
        setStake('');
        loadWaitingRooms();
        alert('Комната создана! Ожидайте соперника.');
      }
    } catch (error) {
      console.error('Error creating room:', error);
      alert('Ошибка при создании комнаты');
    } finally {
      setLoading(false);
    }
  };

  const joinRoom = async (roomId, roomStake) => {
    if (roomStake > balance) {
      alert('Недостаточно средств');
      return;
    }

    setLoading(true);
    try {
      const result = await ApiService.joinCoinflipRoom(roomId, user.telegram_id);
      if (result.success) {
        const newBalance = balance - roomStake + (result.winner === user.telegram_id ? roomStake * 1.8 : 0);
        updateBalance(newBalance);
        loadWaitingRooms();
        
        if (result.winner === user.telegram_id) {
          alert(`Победа! Результат: ${result.result}. Выигрыш: ${(roomStake * 1.8).toFixed(2)} TON`);
        } else {
          alert(`Поражение! Результат: ${result.result}.`);
        }
      }
    } catch (error) {
      console.error('Error joining room:', error);
      alert('Ошибка при присоединении к комнате');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="coinflip-game">
      <motion.div
        className="game-header card-elevated"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="header-icon">
          <Coins size={48} className="text-warning" />
        </div>
        <h2 className="text-title-1">Coinflip PvP</h2>
        <p className="text-callout text-secondary-label">Создайте комнату или присоединитесь к игре других игроков</p>
        <div className="stats-row">
          <div className="stat-item">
            <Users size={16} className="text-primary" />
            <span className="text-footnote">Онлайн: {waitingRooms.length}</span>
          </div>
          <div className="stat-item">
            <Coins size={16} className="text-success" />
            <span className="text-footnote">Ваш баланс: {balance.toFixed(2)} TON</span>
          </div>
        </div>
      </motion.div>

      <motion.div
        className="game-tabs card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <button
          className={`tab-btn ${activeTab === 'rooms' ? 'active' : ''}`}
          onClick={() => setActiveTab('rooms')}
        >
          <Users size={20} />
          <span>Комнаты</span>
          <div className="tab-badge">{waitingRooms.length}</div>
        </button>
        <button
          className={`tab-btn ${activeTab === 'create' ? 'active' : ''}`}
          onClick={() => setActiveTab('create')}
        >
          <Plus size={20} />
          <span>Создать</span>
        </button>
      </motion.div>

      {activeTab === 'rooms' && (
        <motion.div
          className="rooms-list card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="section-header">
            <h3 className="text-title-3">Открытые комнаты</h3>
            <div className="section-badge">
              <Users size={16} />
              <span>{waitingRooms.length}</span>
            </div>
          </div>
          {waitingRooms.length === 0 ? (
            <div className="no-rooms">
              <div className="empty-icon">
                <Coins size={48} className="text-warning" />
              </div>
              <h4 className="text-headline">Нет открытых комнат</h4>
              <p className="text-callout text-secondary-label">Создайте свою комнату и начните игру!</p>
            </div>
          ) : (
            <div className="rooms-grid">
              {waitingRooms.map((room) => (
                <motion.div
                  key={room.id}
                  className="room-card card-elevated"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="room-header">
                    <div className="room-stake">
                      <Coins size={20} className="text-success" />
                      <span className="text-headline">{parseFloat(room.stake).toFixed(2)} TON</span>
                    </div>
                    <div className="room-status">
                      <div className="status-dot"></div>
                      <span className="text-footnote">Ожидает</span>
                    </div>
                  </div>
                  <button
                    className="btn btn-primary w-full"
                    onClick={() => joinRoom(room.id, parseFloat(room.stake))}
                    disabled={loading}
                  >
                    <Play size={20} />
                    Играть
                  </button>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      )}

      {activeTab === 'create' && (
        <motion.div
          className="create-room card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="section-header">
            <h3 className="text-title-3">Создать комнату</h3>
            <div className="section-icon">
              <Plus size={24} className="text-primary" />
            </div>
          </div>

          <div className="create-form">
            <div className="form-group">
              <label className="text-callout font-semibold">Сумма ставки (TON)</label>
              <div className="input-group">
                <Coins size={20} className="input-icon text-success" />
                <input
                  type="number"
                  value={stake}
                  onChange={(e) => setStake(e.target.value)}
                  placeholder="0.00"
                  className="input input-with-icon"
                  disabled={loading}
                  step="0.01"
                  min="0.01"
                />
              </div>
              <div className="form-hint">
                <span className="text-footnote text-tertiary-label">
                  Минимальная ставка: 0.01 TON • Ваш баланс: {balance.toFixed(2)} TON
                </span>
              </div>
            </div>

            <button
              className="btn btn-primary btn-lg w-full"
              onClick={createRoom}
              disabled={loading || !stake}
            >
              {loading ? (
                <Loader2 size={20} className="animate-spin" />
              ) : (
                <Plus size={20} />
              )}
              {loading ? 'Создание...' : 'Создать комнату'}
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default CoinflipGame;
