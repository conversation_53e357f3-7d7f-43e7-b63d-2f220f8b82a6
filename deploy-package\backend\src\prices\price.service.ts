import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Price } from './price.entity';
import * as XLSX from 'xlsx';

@Injectable()
export class PriceService {
  constructor(
    @InjectRepository(Price)
    private priceRepository: Repository<Price>,
  ) {}

  async getAllPrices(): Promise<Price[]> {
    return this.priceRepository.find();
  }

  async getPriceByModelKey(model_key: string): Promise<Price | null> {
    return this.priceRepository.findOne({ where: { model_key } });
  }

  async updatePrice(model_key: string, min_ton: number, image_url: string): Promise<Price> {
    let price = await this.priceRepository.findOne({ where: { model_key } });
    if (price) {
      price.min_ton = min_ton;
      price.image_url = image_url;
    } else {
      price = this.priceRepository.create({
        model_key,
        min_ton,
        image_url,
      });
    }
    return this.priceRepository.save(price);
  }

  async importFromExcel(buffer: Buffer): Promise<{ success: boolean; imported: number; errors: string[] }> {
    const errors: string[] = [];
    let imported = 0;

    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      for (const row of data) {
        try {
          const rowData = row as any;
          const model_key = rowData['model_key'];
          const min_ton = parseFloat(rowData['min_ton']);
          const image_url = rowData['image_url'];

          if (!model_key || isNaN(min_ton) || !image_url) {
            errors.push(`Invalid row: ${JSON.stringify(row)}`);
            continue;
          }

          await this.updatePrice(model_key, min_ton, image_url);
          imported++;
        } catch (error) {
          errors.push(`Error processing row ${JSON.stringify(row)}: ${error.message}`);
        }
      }

      return { success: true, imported, errors };
    } catch (error) {
      return { success: false, imported: 0, errors: [error.message] };
    }
  }

  async deletePrice(model_key: string): Promise<boolean> {
    const result = await this.priceRepository.delete({ model_key });
    return (result.affected || 0) > 0;
  }

  async clearAllPrices(): Promise<void> {
    await this.priceRepository.clear();
  }
}
