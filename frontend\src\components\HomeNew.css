/* Premium Home Component */
.home {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  padding: var(--space-4);
  max-width: 1200px;
  margin: 0 auto;
}

.home-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: var(--space-4);
}

/* Hero Section */
.hero-section {
  position: relative;
  padding: var(--space-4);
  border-radius: 20px !important;
  overflow: hidden;
  min-height: 140px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 122, 255, 0.1) 0%,
    rgba(191, 90, 242, 0.1) 50%,
    rgba(255, 159, 10, 0.1) 100%);
  opacity: 0.6;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.welcome-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-1);
}

.welcome-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--tertiary-system-background);
  border-radius: 14px;
  border: 1px solid var(--separator);
}

.welcome-text {
  flex: 1;
}

.balance-card {
  background: var(--tertiary-system-background);
  border: 1px solid var(--separator);
  border-radius: 16px;
  padding: var(--space-3);
  text-align: center;
}

.balance-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
  margin-bottom: var(--space-1);
}

.balance-amount {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: var(--space-1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-2);
}

.stat-card {
  background: var(--tertiary-system-background);
  border: 1px solid var(--separator);
  border-radius: 12px;
  padding: var(--space-2);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-1);
  text-align: center;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: var(--secondary-system-background);
  border-radius: 8px;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

/* Games Section */
.games-section {
  padding: var(--space-4);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--separator);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.section-badge {
  padding: var(--space-1) var(--space-2);
  background: var(--tertiary-system-background);
  border-radius: 8px;
  border: 1px solid var(--separator);
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-4);
}

.game-card {
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  cursor: pointer;
  transition: all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  min-height: 160px;
}

.game-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.game-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 14px;
  border: 1px solid var(--separator);
}

.game-status {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.status-dot {
  width: 8px;
  height: 8px;
  background: var(--system-purple);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.game-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.game-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

.game-stats {
  display: flex;
  gap: var(--space-3);
}

.stat-mini {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  color: var(--tertiary-label);
}

.play-btn {
  background: var(--system-blue);
  color: #FFFFFF;
  border: none;
  border-radius: 12px;
  padding: var(--space-2) var(--space-3);
  cursor: pointer;
  transition: all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.play-btn:hover {
  background: #0056CC;
  transform: translateY(-1px);
}

/* Leaderboard Section */
.leaderboard-section {
  padding: var(--space-4);
}

.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.leaderboard-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--tertiary-system-background);
  border: 1px solid var(--separator);
  border-radius: 16px;
  transition: all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.leaderboard-item:hover {
  background: var(--secondary-system-background);
  transform: translateY(-1px);
}

.player-rank {
  display: flex;
  align-items: center;
  justify-content: center;
}

.rank-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: var(--secondary-system-background);
  border: 1px solid var(--separator);
}

.rank-badge.top-three {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
  border-color: rgba(255, 215, 0, 0.3);
}

.rank-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.player-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.player-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.player-balance {
  display: flex;
  align-items: baseline;
  gap: var(--space-1);
  text-align: right;
}

.result-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 8px;
  background: var(--tertiary-system-background);
}

/* Responsive */
@media (max-width: 768px) {
  .home {
    padding: var(--space-3);
  }
  
  .hero-section {
    padding: var(--space-4);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .games-grid {
    grid-template-columns: 1fr;
  }
  
  .welcome-header {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
  }
}

@media (max-width: 480px) {
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .player-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
}
