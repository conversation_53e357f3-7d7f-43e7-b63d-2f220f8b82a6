import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App.jsx';
import { TonConnectUIProvider } from '@tonconnect/ui-react';

// Инициализируем Telegram WebApp скрипт
const script = document.createElement('script');
script.src = 'https://telegram.org/js/telegram-web-app.js';
script.async = true;
document.head.appendChild(script);

// Ждем загрузки скрипта и рендерим приложение
script.onload = () => {
  console.log('Telegram WebApp script loaded');

  createRoot(document.getElementById('root')).render(
    <StrictMode>
      <TonConnectUIProvider manifestUrl="https://ton-connect.github.io/demo-dapp-with-react-ui/tonconnect-manifest.json">
        <App />
      </TonConnectUIProvider>
    </StrictMode>,
  );
};

// Fallback если скрипт не загрузился
script.onerror = () => {
  console.warn('Failed to load Telegram WebApp script, rendering anyway');

  createRoot(document.getElementById('root')).render(
    <StrictMode>
      <TonConnectUIProvider manifestUrl="https://ton-connect.github.io/demo-dapp-with-react-ui/tonconnect-manifest.json">
        <App />
      </TonConnectUIProvider>
    </StrictMode>,
  );
};
