#!/bin/bash

echo "🚀 Deploying PEPE CAS Mini App..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f "backend/.env" ]; then
    print_warning ".env file not found. Creating template..."
    cat > backend/.env << EOF
# Telegram Bot Token (получите у @BotFather)
TELEGRAM_BOT_TOKEN=your_bot_token_here

# Database
DATABASE_URL=file:./database.sqlite

# Server
PORT=3000
NODE_ENV=production
EOF
    print_warning "Please edit backend/.env and add your Telegram bot token"
    exit 1
fi

# Create data directory
mkdir -p data
mkdir -p ssl

# Check if SSL certificates exist
if [ ! -f "ssl/cert.pem" ] || [ ! -f "ssl/key.pem" ]; then
    print_warning "SSL certificates not found. Generating self-signed certificates..."
    openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
    print_warning "Self-signed certificates generated. For production, use proper SSL certificates."
fi

# Build and start containers
print_status "Building Docker containers..."
docker-compose down
docker-compose build --no-cache

print_status "Starting services..."
docker-compose up -d

# Wait for services to start
print_status "Waiting for services to start..."
sleep 10

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    print_status "✅ Deployment successful!"
    print_status "🌐 Application is running at:"
    print_status "   HTTP:  http://localhost"
    print_status "   HTTPS: https://localhost"
    print_status "   API:   https://localhost/api"
    echo ""
    print_status "📱 To connect to Telegram:"
    print_status "1. Get your server's public IP or domain"
    print_status "2. Update your bot's Web App URL to: https://your-domain.com"
    print_status "3. Test the mini app in Telegram"
    echo ""
    print_status "📊 View logs with: docker-compose logs -f"
    print_status "🛑 Stop with: docker-compose down"
else
    print_error "❌ Deployment failed. Check logs with: docker-compose logs"
    exit 1
fi
